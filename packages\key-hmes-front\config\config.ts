import { extendParentConfig } from '@hzerojs/plugin-micro';

export default extendParentConfig({
  webpack5: {},
  routes: [
    // 不良评审单管理平台
    //  {
    //   path: '/hwms/ncReport-doc-maintain-new',
    //   routes: [
    //     {
    //       path: '/hwms/ncReport-doc-maintain-new/list',
    //       component: '@/routes/hwms/NewNcReportDocMaintain/NcReportList',
    //     },
    //     {
    //       path: '/hwms/ncReport-doc-maintain-new/dist/:id',
    //       component: '@/routes/hwms/NewNcReportDocMaintain/NcReportDetail',
    //     },
    //   ],
    // },
    // 检验单维护
    {
      path: '/hwms/inspect-doc-maintain',
      priority: 10,
      routes: [
        {
          path: '/hwms/inspect-doc-maintain/list',
          priority: 10,
          component: '@/routes/InspectDocMaintain/InspectDocList',
        },
        {
          path: '/hwms/inspect-doc-maintain/dist/:id',
          priority: 10,
          component: '@/routes/InspectDocMaintain/InspectDocDetail',
        },
      ],
    },
    // 检验属性维护功能
    {
      path: '/hmes/product/inspection-attributes',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/inspection-attributes/list',
          priority: 10,
          component: '@/routes/InspectionAttributes/InspectionAttributesList',
        },
      ],
    },
    // 用户检验权限维护
    {
      path: '/hwms/user-inspect-permission',
      priority: 10,
      routes: [
        {
          path: '/hwms/user-inspect-permission/list',
          priority: 10,
          component: '@/routes/UserInspectPermission',
        },
        {
          path: '/hwms/user-inspect-permission/detail/:id/:siteId',
          priority: 10,
          component: '@/routes/UserInspectPermission/userDetail',
        },
      ],
    },
    // 检验平台仓库
    {
      path: '/hwms/inspection-platform',
      priority: 10,
      routes: [
        {
          path: '/hwms/inspection-platform/list',
          priority: 10,
          component: '@/routes/InspectionPlatform/InspectionPlatformList',
        },
        {
          path: '/hwms/inspection-platform/dist/:id',
          priority: 10,
          component: '@/routes/InspectionPlatform/InspectionPlatformDetail',
        },
        {
          path: '/hwms/inspection-platform/inspect-doc/:id',
          priority: 10,
          component: '@/routes/InspectDocMaintain/InspectDocDetail',
        },
        {
          path: '/hwms/inspection-platform/:code',
          priority: 10,
          component: '@/routes/InspectionPlatform/InspectionPlatformList/CommentImport',
        },
      ],
    },
    // 检验平台车间
    {
      path: '/hwms/inspection-platform-workshop',
      priority: 10,
      routes: [
        {
          path: '/hwms/inspection-platform-workshop/list',
          priority: 10,
          component: '@/routes/InspectionPlatformWorkshop/InspectionPlatformList',
        },
        {
          path: '/hwms/inspection-platform-workshop/dist/:id',
          priority: 10,
          component: '@/routes/InspectionPlatformWorkshop/InspectionPlatformDetail',
        },
        {
          path: '/hwms/inspection-platform-workshop/inspect-doc/:id',
          priority: 10,
          component: '@/routes/InspectDocMaintain/InspectDocDetail',
        },
        {
          path: '/hwms/inspection-platform-workshop/:code',
          priority: 10,
          component: '@/routes/InspectionPlatformWorkshop/InspectionPlatformList/CommentImport',
        },
      ],
    },
    // 不良记录单管理平台
    {
      path: '/hwms/ncReport-doc-maintain',
      routes: [
        {
          path: '/hwms/ncReport-doc-maintain/list',
          component: '@/routes/NcReportDocMaintain/NcReportList',
        },
        {
          path: '/hwms/ncReport-doc-maintain/dist/:id',
          component: '@/routes/NcReportDocMaintain/NcReportDetail',
        },
      ],
    },
    // 检验项目维护
    {
      path: '/hwms/inspect-item-maintain',
      priority: 10,
      routes: [
        {
          path: '/hwms/inspect-item-maintain/list',
          priority: 10,
          component: '@/routes/InspectItemMaintain/InspectItemList',
        },
        {
          path: '/hwms/inspect-item-maintain/dist/:id',
          priority: 10,
          component: '@/routes/InspectItemMaintain/InspectItemDetail',
        },
      ],
    },
    // 检验项目组维护
    {
      priority: 10,
      path: '/hwms/inspect-group-maintenance',
      routes: [
        {
          priority: 10,
          path: '/hwms/inspect-group-maintenance/list',
          component: '@/routes/InspectGroupMaintenance/InspectGroupList',
        },
        {
          path: '/hwms/inspect-group-maintenance/dist/:id',
          priority: 10,
          component: '@/routes/InspectGroupMaintenance/InspectGroupDetail',
        },
      ],
    },
    // 检验方案维护
    {
      path: '/hwms/inspection-scheme-maintenance',
      priority: 10,
      routes: [
        {
          path: '/hwms/inspection-scheme-maintenance/list',
          priority: 10,
          component: '@/routes/InspectionScheme/InspectionSchemeList',
        },
        {
          path: '/hwms/inspection-scheme-maintenance/detail/:id',
          priority: 10,
          component: '@/routes/InspectionScheme/InspectionSchemeDetail',
        },
      ],
    },
    // 检验方案模板维护
    {
      path: '/hwms/inspection-scheme-template-maintenance',
      priority: 1000,
      routes: [
        {
          path: '/hwms/inspection-scheme-template-maintenance/list',
          component: '@/routes/InspectionSchemeTemplate/InspectionSchemeTemplateList',
          priority: 1000,
        },
        {
          path: '/hwms/inspection-scheme-template-maintenance/detail/:id',
          component: '@/routes/InspectionSchemeTemplate/InspectionSchemeTemplateDetail',
          priority: 1000,
        },
      ],
    },
    // 报检信息管理平台
    {
      path: '/hwms/inspection-info-management',
      priority: 10,
      routes: [
        {
          path: '/hwms/inspection-info-management/list',
          component: '@/routes/InspectionInfoManagement/List',
          priority: 10,
        },
      ],
    },
    // OOC统计报表
    {
      path: '/hspc/ooc-report',
      priority: 999,
      component: '@/routes/OocReport/OocReportList',
    },
    // 生产指令管理
    {
      path: '/hmes/workshop/production-order-mgt',
      priority: 10,
      routes: [
        {
          path: '/hmes/workshop/production-order-mgt/list',
          priority: 10,
          component: '@/routes/workshop/ProductionOrderMgt/ProductionOrderMgtList',
        },
        {
          path: '/hmes/workshop/production-order-mgt/detail/:id',
          priority: 10,
          component: '@/routes/workshop/ProductionOrderMgt/ProductionOrderMgtDetail',
        },
      ],
    },

    // 处置方法维护
    {
      path: '/hmes/bad/disposition-method',
      priority: 999,
      component: '@/routes/DispositionMethod/DispositionMethodList',
    },
    // 库位维护
    {
      path: '/hmes/organization-modeling/locator',
      priority: 10,
      routes: [
        {
          path: '/hmes/organization-modeling/locator/list',
          component: '@/routes/org/Locator/LocatorList',
          priority: 10,
        },
        {
          path: '/hmes/organization-modeling/locator/detail/:locatorId',
          component: '@/routes/org/Locator/LocatorDetail',
          priority: 10,
        },
      ],
    },
    // 打印模板配置
    {
      path: '/hmes/print-template-configuration',
      authorized: true,
      routes: [
        {
          path: '/hmes/print-template-configuration/list',
          component: '@/routes/PrintTemplateConfiguration/index.js',
          authorized: true,
        },
        {
          path: '/hmes/print-template-configuration/detail/:templateId',
          component: '@/routes/PrintTemplateConfiguration/detail.js',
          authorized: true,
        },
      ],
    },

    // 事件查询
    {
      path: '/hmes/event/quality/event/query',
      component: '@/routes/qualityEvent/EventQuery',
    },
    // 事件请求类型维护
    {
      path: '/hmes/event/quality/event-request-type',
      component: '@/routes/qualityEvent/EventRequestTypeDemo',
    },
    // 事件对象类型维护
    {
      path: '/hmes/event/quality/object-type',
      component: '@/routes/qualityEvent/ObjectTypeNew',
    },
    // 事件类型维护
    {
      path: '/hmes/event/quality/event-type',
      component: '@/routes/qualityEvent/EventType',
    },
    // 用户权限维护
    {
      path: '/hmes/mes/user-rights',
      priority: 10,
      component: '@/routes/hmes/UserRights',
    },
    // 质量追溯报表
    {
      path: '/hmes/quality-traceability-report',
      component: '@/routes/QualityTraceabilityReport',
    },
    // MARKING标记统计报表
    {
      path: '/hmes/marking-tag-statistical-report',
      component: '@/routes/MarkingTagStatisticalReport',
    },
    // MARKING标记统计报表
    {
      path: '/hmes/marking-tag-statistical-report-detail',
      authorized: true,
      title: 'Marking趋势历史',
      component: '@/routes/MarkingTagStatisticalReportDetail',
    },
    // 员工上下岗
    {
      path: '/group/employee-clock',
      priority: 999,
      routes: [
        {
          path: '/group/employee-clock/list',
          component: '@/routes/group/EmployeeClock/EmployeeClockList',
          priority: 999,
        },
      ],
    },
    // 检验严格度管理
    {
      path: '/hmes/management-of-inspection-strictness-status',
      component: '@/routes/ManagementOfInspectionStrictnessStatus',
    },
    // 消息处理查询
    {
      path: '/message/message-processing',
      title: '消息处理查询',
      component: '@/routes/message/MessageProcessing',
    },
    // 检验用时报表
    {
      path: '/hwms/inspect-rate-report',
      title: '点检/维保完成率报表',
      component: '@/routes/InspectionRateReport',
    },
    // 检验追溯报表
    {
      path: '/hwms/inspect-trace-report',
      title: '检验追溯报表',
      component: '@/routes/InspectTraceReport/InspectTraceList',
    },
    // 检验数据趋势报表
    {
      path: '/hwms/inspect-trend-report',
      title: '检验数据趋势报表',
      component: '@/routes/InspectionTrendReport',
    },
    // 检验用时报表
    {
      path: '/hwms/inspect-time-report',
      title: '检验用时报表',
      component: '@/routes/InspectTimeReport',
    },
    // 检验不良报表
    {
      path: '/hwms/inspect-bad-report',
      title: '检验不良报表',
      component: '@/routes/InspectBadReport',
    },
    // Bartender模版维护
    {
      path: '/hmes/bartender-template-maintain',
      title: 'Bartender模版维护',
      component: '@/routes/BartenderTemplateMaintain/List',
    },
    // 指令模版维护
    {
      path: '/hmes/print-instruction-maintain',
      title: '指令模版维护',
      component: '@/routes/PrintInstructionMaintain/List',
    },
    // 抽样方式维护
    {
      path: '/sampling/sampling-method/sampling-mode',
      routes: [
        {
          path: '/sampling/sampling-method/sampling-mode/list',
          component: '@/routes/samplingMethod/SamplingMode/SamplingModeList',
        },
        {
          path: '/sampling/sampling-method/sampling-mode/detail/:id',
          component: '@/routes/samplingMethod/SamplingMode/SamplingModeDetail',
        },
      ],
    },

    // 产品加工履历查询报表
    // {
    //   path: '/hmes/product-processing-history-query-report',
    //   component: '@/routes/ProductProcessingHistoryQueryReport',
    // },
    // {
    //   title: '装配记录查询',
    //   path: '/hmes/assembly-record-query',
    //   component: '@/routes/AssemblyRecordQuery',
    // },

    // 产品加工参数查询报表
    {
      path: '/hmes/product-processing-parameter-query',
      component: '@/routes/ProductProcessingParameterQuery',
    },

    // 报工事物报表平台
    {
      path: '/hmes/work-transaction-report/platform',
      component: '@/routes/transactionReport/WorkTransactionReportPlatform',
    },

    // 报工事物明细报表
    {
      path: '/hmes/work-transaction-report/transaction-detail-report',
      component: '@/routes/transactionReport/WorkTransactionDetailReport',
    },

    // 报工事件明细报表
    {
      path: '/hmes/work-transaction-report/mobile-event-detail-report',
      component: '@/routes/transactionReport/WorkMobileEventDetailReport',
    },
    // 报废报表
    {
      path: '/hmes/scrap-report',
      authoried: true,
      component: '@/routes/ScrapReport',
    },

    {
      path: '/hspc/cpk-comparison-chart',
      component: '@/routes/hspc/CPKComparisonChart',
    },
    {
      path: '/hspc/cpk-trend-chart',
      component: '@/routes/hspc/CPKTrendChart',
    },
    {
      path: '/hspc/control-chart-process-object',
      routes: [
        {
          path: '/hspc/control-chart-process-object/list',
          component: '@/routes/hspc/ControlChartProcessObject',
          authoried: true,
          title: '控制图过程对象维护-列表页',
        },
      ],
    },
    // 对象数据收集组管关系查询
    {
      path: '/hmes/acquisition/tag-group-object',
      routes: [
        {
          path: '/hmes/acquisition/tag-group-object/list',
          component: '@/routes/TagGroupObjectQuery/TagGroupObjectList',
        },
      ],
    },
    // 不良代码维护
    {
      path: '/hmes/bad/defect-code',
      priority: 10,
      routes: [
        {
          path: '/hmes/bad/defect-code/list',
          component: '@/routes/bad/DefectCode/DefectCodeList',
          priority: 10,
        },
        {
          path: '/hmes/bad/defect-code/dist/:id',
          component: '@/routes/bad/DefectCode/DefectCodeDist',
          priority: 10,
        },
      ],
    },
    // 不良代码组维护
    {
      path: '/hmes/bad/defect-group',
      priority: 10,
      routes: [
        {
          path: '/hmes/bad/defect-group/list',
          component: '@/routes/bad/DefectGroup/DefectGroupList',
          priority: 10,
        },
        {
          path: '/hmes/bad/defect-group/dist/:id',
          component: '@/routes/bad/DefectGroup/DefectGroupDist',
          priority: 10,
        },
      ],
    },
    // 事务类型接口关系
    {
      path: '/ne-hmes/event-type-interface-relationship',
      component: '@/routes/commonConfig/EventTypeInterfaceRelationship',
    },

    // 指令执行规则维护
    {
      path: '/ne-hmes/commonConfig/order-execute-rule',
      priority: 10,
      routes: [
        {
          path: '/ne-hmes/commonConfig/order-execute-rule/list',
          component: '@/routes/commonConfig/OrderExecuteRule',
          priority: 10,
        },
        {
          path: '/ne-hmes/commonConfig/order-execute-rule/detail/:id',
          component: '@/routes/commonConfig/OrderExecuteRule/Detail',
          priority: 10,
        },
      ],
    },
    // 指令执行规则维护(新)
    {
      path: '/ne-hmes/commonConfig/order-execute-rule-new',
      routes: [
        {
          path: '/ne-hmes/commonConfig/order-execute-rule-new/list',
          component: '@/routes/commonConfig/OrderExecuteRuleNew',
        },
        {
          path: '/ne-hmes/commonConfig/order-execute-rule-new/detail/:id',
          component: '@/routes/commonConfig/OrderExecuteRuleNew/Detail',
        },
      ],
    },

    // 静默期维护
    {
      path: '/ne-hmes/silent-period-maintenance',
      component: '@/routes/commonConfig/SilentPeriodMaintenance',
    },

    // 成品条码注册
    {
      path: '/ne-hmes/inbound/bar-code-registration',
      priority: 10,
      routes: [
        {
          path: '/ne-hmes/inbound/bar-code-registration/list',
          component: '@/routes/inbound/BarCodeRegistration/RegistrationList',
          priority: 10,
        },
      ],
    },

    // 入库单查询
    {
      path: '/ne-hmes/inbound/inbound-order-query',
      priority: 10,
      routes: [
        {
          path: '/hmes/inbound/inbound-order-query/list',
          priority: 10,
          component: '@/routes/inbound/InboundOrderQuery/InboundOrderQueryList',
        },
      ],
    },
    // 入库单查询（重构版）
    {
      path: '/ne-hmes/inbound/inbound-order-query-standard',
      routes: [
        {
          path: '/ne-hmes/inbound/inbound-order-query-standard/list',
          component: '@/routes/inbound/InboundOrderQueryStandard/InboundOrderQueryList',
        },
      ],
    },

    // 成本中心维护
    {
      path: '/ne-hmes/in-library/cost-order-maintain',
      priority: 10,
      routes: [
        {
          path: '/ne-hmes/in-library/cost-order-maintain/list',
          component: '@/routes/inLibrary/CostOrderMaintain/List',
          priority: 10,
        },
      ],
    },

    // 杂项工作台
    {
      path: '/ne-hmes/in-library/miscellaneous',
      priority: 10,
      routes: [
        {
          path: '/ne-hmes/in-library/miscellaneous/list',
          priority: 10,
          component: '@/routes/inLibrary/miscellaneous',
        },
        {
          path: '/ne-hmes/in-library/miscellaneous/detail/:id',
          priority: 10,
          component: '@/routes/inLibrary/miscellaneous/Detail',
        },
      ],
    },
    // 杂项工作台(重构版)
    {
      path: '/ne-hmes/in-library/miscellaneous-standard',
      routes: [
        {
          path: '/ne-hmes/in-library/miscellaneous-standard/list',
          component: '@/routes/inLibrary/miscellaneousStandard',
        },
        {
          path: '/ne-hmes/in-library/miscellaneous-standard/detail/:id',
          component: '@/routes/inLibrary/miscellaneousStandard/Detail',
        },
      ],
    },
    // 库存调拨平台
    {
      path: '/ne-hmes/in-library/send-receive-doc-new',
      priority: 10,
      routes: [
        {
          path: '/ne-hmes/in-library/send-receive-doc-new/list',
          priority: 10,
          component: '@/routes/inLibrary/SendReceiveDoc/SendReceiveList',
        },
        {
          path: '/ne-hmes/in-library/send-receive-doc-new/detail/:id',
          priority: 10,
          component: '@/routes/inLibrary/SendReceiveDoc/SendReceiveDetail',
        },
      ],
    },
    // 库存调拨平台(重构版)
    {
      path: '/ne-hmes/in-library/send-receive-doc-standard',
      routes: [
        {
          path: '/ne-hmes/in-library/send-receive-doc-standard/list',
          component: '@/routes/inLibrary/SendReceiveDocStandard/SendReceiveList',
        },
        {
          path: '/ne-hmes/in-library/send-receive-doc-standard/detail/:id',
          component: '@/routes/inLibrary/SendReceiveDocStandard/SendReceiveDetail',
        },
      ],
    },

    // 报检请求管理平台
    {
      path: '/hmes/inspection/inspection-management',
      priority: 10,
      routes: [
        {
          path: '/hmes/inspection/inspection-management/list',
          component: '@/routes/inspection/InspectionManagement',
          priority: 10,
        },
      ],
    },

    // 盘点工作台
    {
      path: '/ne-hmes/inventory/inventory-workbench',
      priority: 10,
      routes: [
        {
          path: '/ne-hmes/inventory/inventory-workbench/list',
          component: '@/routes/inventory/StocktakeWorkbench/StocktakeWorkbenchList',
          priority: 10,
        },
        {
          path: '/ne-hmes/inventory/inventory-workbench/detail/:id',
          component: '@/routes/inventory/StocktakeWorkbench/StocktakeWorkbenchDetail',
          priority: 10,
        },
      ],
    },

    // 库存查询
    {
      path: '/ne-hmes/inventory/query/:timer?',
      priority: 10,
      component: '@/routes/inventory/Query/QueryList',
    },

    // 容器管理平台
    {
      path: '/ne-hmes/product/container-management-platform',
      priority: 10,
      routes: [
        {
          path: '/ne-hmes/product/container-management-platform/list',
          component: '@/routes/inventory/ContainerManagePlatform',
          priority: 10,
        },
      ],
    },

    // 容器类型维护
    {
      path: '/ne-hmes/hagd/container-type',
      priority: 10,
      routes: [
        {
          path: '/ne-hmes/hagd/container-type/list',
          component: '@/routes/inventory/ContainerType',
          priority: 10,
        },
        {
          path: '/ne-hmes/hagd/container-type/detail/:id',
          component: '@/routes/inventory/ContainerType/ContainerTypeDetail',
          priority: 10,
        },
      ],
    },

    // 库存初始化
    {
      path: '/ne-hmes/inventory/initial',
      priority: 10,
      component: '@/routes/inventory/InventoryInitial',
    },

    // 库存日记账查询
    {
      path: '/ne-hmes/inventory/journal/query',
      priority: 10,
      component: '@/routes/inventory/JournalQuery/JournalQueryList',
    },

    // 库存预留日记账查询
    {
      path: '/ne-hmes/inventory/reserve/query',
      component: '@/routes/inventory/ReserveQuery/ReserveQueryList',
      priority: 10,
    },

    // 物料批管理平台
    {
      path: '/ne-hmes/product/material-lot-traceability',
      priority: 10,
      routes: [
        {
          path: '/ne-hmes/product/material-lot-traceability/list/:timer?',
          component: '@/routes/inventory/MaterialLotTrace',
          priority: 10,
        },
        {
          path: '/ne-hmes/product/material-lot-traceability/detail/:id',
          component: '@/routes/inventory/MaterialLotTrace/MaterialLotTraceDetail',
          priority: 10,
        },
      ],
    },

    // 领退料工作台(重构版)
    {
      path: '/ne-hmes/receive/receive-return-standard',
      routes: [
        {
          path: '/ne-hmes/receive/receive-return-standard/list',
          component: '@/routes/receive/ReceiveReturnStandard',
        },
        {
          path: '/ne-hmes/receive/receive-return-standard/detail/:id/:docType/:docTypeTag',
          component: '@/routes/receive/ReceiveReturnStandard/Detail',
        },
      ],
    },

    // 事件请求类型维护
    {
      path: '/hmes/event/event-request-type',
      priority: 10,
      component: '@/routes/event/EventRequestTypeDemo',
    },

    // 事件类型维护
    {
      path: '/hmes/event/event-type',
      priority: 10,
      component: '@/routes/event/EventType',
    },

    // 事件对象类型维护
    {
      path: '/ne-hmes/event/object-type',
      priority: 10,
      component: '@/routes/event/ObjectTypeNew',
    },

    {
      title: '事件查询',
      priority: 10,
      path: '/hmes/event/query',
      component: '@/routes/event/EventQuery',
    },

    // 送货单管理(重构版)
    {
      path: '/ne-hmes/purchase/delivery-management-standard',
      routes: [
        {
          path: '/ne-hmes/purchase/delivery-management-standard/list',
          component: '@/routes/purchase/DeliveryStandard',
        },
        {
          path: '/ne-hmes/purchase/delivery-management-standard/detail/:id',
          component: '@/routes/purchase/DeliveryStandard/Detail',
        },
      ],
    },

    // 销售订单管理(重构版)
    {
      path: '/ne-hmes/so-delivery/sell-order-manage-standard',
      routes: [
        {
          path: '/ne-hmes/so-delivery/sell-order-manage-standard/list',
          component: '@/routes/soDelivery/SellOrderStandard',
        },
        {
          path: '/ne-hmes/so-delivery/sell-order-manage-standard/detail/:id',
          component: '@/routes/soDelivery/SellOrderStandard/SellOrderDetail',
        },
      ],
    },

    // 外协管理平台(重构版)
    {
      path: '/ne-hmes/purchase/outsourcing-manage-standard',
      routes: [
        {
          path: '/ne-hmes/purchase/outsourcing-manage-standard/list',
          component: '@/routes/purchase/OutsourcingStandard/OutsourcingList',
        },
        // 创建外协发料单
        {
          path: '/ne-hmes/purchase/outsourcing-manage-standard/outsourcing-doc/create',
          component: '@/routes/purchase/OutsourcingStandard/OrderCreate',
        },
        // 创建外协退料单
        {
          path: '/ne-hmes/purchase/outsourcing-manage-standard/outsourcing-returns/create',
          component: '@/routes/purchase/OutsourcingStandard/OutsourcingReturn',
        },
        // 创建外协补料单
        {
          path: '/ne-hmes/purchase/outsourcing-manage-standard/supplement/:id',
          component: '@/routes/purchase/OutsourcingStandard/SupplementBill',
        },
      ],
    },

    // 采购退货平台(重构版)
    {
      path: '/ne-hmes/purchase/purchase-return-standard',
      routes: [
        {
          path: '/ne-hmes/purchase/purchase-return-standard/list',
          component: '@/routes/purchase/PurchaseReturnStandard/PurchaseList',
        },
        {
          path: '/ne-hmes/purchase/purchase-return-standard/detail/:id',
          component: '@/routes/purchase/PurchaseReturnStandard/PurchaseDetail',
        },
      ],
    },

    // 超期报表
    {
      path: '/ne-hmes/report/over-due',
      priority: 10,
      component: '@/routes/report/OverDue',
    },

    // 呆滞报表
    {
      path: '/ne-hmes/sluggish-report',
      component: '@/routes/report/SluggishReport',
    },

    // 销单库存转移-位于依赖hcm-mes-front
    {
      path: '/ne-hmes/inventory/so-stock-transfer',
      component: '@/routes/inventory/SOStockTransfer/SOStockTransferList',
      priority: 10,
    },

    // 销售订单管理(重构版)
    {
      path: '/ne-hmes/so-delivery/sell-order-manage-standard',
      routes: [
        {
          path: '/ne-hmes/so-delivery/sell-order-manage-standard/list',
          component: '@/routes/soDelivery/SellOrderStandard',
        },
        {
          path: '/ne-hmes/so-delivery/sell-order-manage-standard/detail/:id',
          component: '@/routes/soDelivery/SellOrderStandard/SellOrderDetail',
        },
      ],
    },

    // 销售发运平台(重构版)
    {
      path: '/ne-hwms/so-delivery/so-delivery-platform-standard',
      routes: [
        {
          path: '/ne-hwms/so-delivery/so-delivery-platform-standard/list',
          component: '@/routes/soDelivery/SoDeliveryPlatformStandard/SoDeliveryList',
        },
        {
          path: '/ne-hwms/so-delivery/so-delivery-platform-standard/detail/:id',
          component: '@/routes/soDelivery/SoDeliveryPlatformStandard/SoDeliveryDetail',
        },
      ],
    },

    // 寻址策略
    {
      path: '/ne-hmes/strategy/addressing',
      priority: 10,
      routes: [
        {
          path: '/ne-hmes/strategy/addressing/list',
          priority: 10,
          component: '@/routes/strategy/Addressing/StrategyList',
        },
        {
          path: '/ne-hmes/strategy/addressing/detail/:id',
          priority: 10,
          component: '@/routes/strategy/Addressing/StrategyDetail',
        },
        {
          path: '/ne-hmes/strategy/addressing/distribution/:id',
          priority: 10,
          component: '@/routes/strategy/Addressing/StrategyDistribution',
        },
      ],
    },

    // 物料库位关系维护
    {
      path: '/ne-hmes/strategy/locator-relation',
      priority: 10,
      routes: [
        {
          path: '/ne-hmes/strategy/locator-relation/list',
          component: '@/routes/strategy/LocatorRelation/LocatorRelationList',
          priority: 10,
        },
        {
          path: '/ne-hmes/strategy/locator-relation/batch-import',
          component: '@/routes/strategy/LocatorRelation/BatchImport',
          priority: 10,
        },
      ],
    },

    // 编码规则维护
    {
      path: '/hmes/mes/maintain-number-new',
      routes: [
        {
          path: '/hmes/mes/maintain-number-new/list',
          component: '@/routes/MaintainNumber/MaintainNumberList',
          model: '@/models/hmes/maintainNumber',
        },
        {
          path: '/hmes/mes/maintain-number-new/detail/:id',
          component: '@/routes/MaintainNumber/MaintainNumberDetail',
          model: '@/models/hmes/maintainNumber',
        },
      ],
    },

    // 不良记录创建-MES
    {
      path: '/hmes/bad-record/platform-new',
      routes: [
        {
          path: '/hmes/bad-record/platform-new/list',
          component: '@/routes/badRecordCreate/Platform/List',
        },
        {
          path: '/hmes/bad-record/platform-new/detail/:id',
          component: '@/routes/badRecordCreate/Platform/Detail',
        },
        {
          path: '/hmes/bad-record/platform-new/print/:id',
          component: '@/routes/badRecordCreate/Platform/Print',
        },
      ],
    },

    // 报废入库单创建（试制）
    {
      path: '/hmes/creation-of-scrap-inventory-form-trial',
      component: '@/pages/CreationOfScrapInventoryFormTrial',
      title: '报废入库单创建（试制）',
    },

    // 批号信息查询
    {
      path: '/hmes/batch-number-information-query',
      component: '@/pages/BatchNumberInformationQuery',
      title: '批号信息查询',
    },

    // 物料站点属性维护
    {
      path: '/hmes/product/material-site-properties',
      priority: 1000,
      routes: [
        {
          path: '/hmes/product/material-site-properties/list',
          priority: 1000,
          component: '@/routes/product/MaterialSiteProperties/MaterialSitePropertiesList',
        },
        {
          path: '/hmes/product/material-site-properties/detail/:materialId/:materialSiteId',
          priority: 1000,
          component: '@/routes/product/MaterialSiteProperties/MaterialSitePropertiesDetail',
        },
      ],
    },

    // 报废物料映射关系维护
    {
      path: '/hmes/maintenance-mapping-relationship-scrapped-materials',
      title: '报废物料映射关系维护',
      routes: [
        {
          path: '/hmes/maintenance-mapping-relationship-scrapped-materials/list',
          component: '@/pages/MaintenanceMappingRelationshipScrappedMaterials',
        },
        {
          path: '/hmes/maintenance-mapping-relationship-scrapped-materials/comment-import/:code',
          component: '@/components/CommentImport',
          authorized: true,
        },
      ],
    },
    {
      path: '/hspc/analysis-control-chart',
      priority: 10,
      routes: [
        {
          path: '/hspc/analysis-control-chart/list',
          component: '@/routes/hspc/AnalysisControlChart/AnalysisControlChartList',
          priority: 10,
        },
        {
          path: '/hspc/analysis-control-chart/new/display/',
          component: '@/routes/hspc/AnalysisControlChart/AnalysisControlChartDisplayNew',
          priority: 10,
        },
        {
          path: '/hspc/analysis-control-chart/display/:type/:id',
          component: '@/routes/hspc/AnalysisControlChart/AnalysisControlChartDisplay',
          priority: 10,
        },
        {
          path: '/hspc/analysis-control-chart/create-page/:id',
          component: '@/routes/hspc/AnalysisControlChart/AnalysisControlChartCreatePage',
          priority: 10,
        },
      ],
    },
    {
      path: '/hspc/control-chart',
      priority: 10,
      routes: [
        {
          path: '/hspc/control-chart/list',
          component: '@/routes/hspc/ControlChart/ControlChartList',
          priority: 10,
        },
        {
          path: '/hspc/control-chart/detail/:id',
          component: '@/routes/hspc/ControlChart/ControlChartDetail',
          priority: 10,
        },
        {
          path: '/hspc/control-chart/create-page/:code',
          component: '@/routes/hspc/ControlChart/ControlChartCreatePage',
          priority: 10,
        },
        {
          path: '/hspc/control-chart/display/:id',
          component: '@/routes/hspc/ControlChart/ControlChartDisplay',
          priority: 10,
        },
        {
          path: '/hspc/control-chart/history-chart/:id',
          component: '@/routes/hspc/ControlChart/HistoryChartDisplay',
          priority: 10,
        },
        {
          path: '/hspc/control-chart/alarm-message/:id',
          component: '@/routes/hspc/ControlChart/AlarmMessages',
          priority: 10,
        },
        {
          path: '/hspc/control-chart/manual-access/:id',
          component: '@/routes/hspc/ControlChart/DataAccess/ManualAccess',
          priority: 10,
        },
      ],
    },
    {
      path: '/hspc/subject-maintain',
      priority: 10,
      routes: [
        {
          path: '/hspc/subject-maintain/list',
          component: '@/routes/hspc/SubjectMaintain/SubjectMaintainList',
          priority: 10,
        },
        {
          path: '/hspc/subject-maintain/detail/:id',
          component: '@/routes/hspc/SubjectMaintain/SubjectMaintainDetail',
          priority: 10,
        },
        {
          path: '/hspc/subject-maintain/overview/:id',
          component: '@/routes/hspc/SubjectMaintain/SubjectOverview',
          priority: 10,
        },
      ],
    },
    // 物料类别维护
    {
      path: '/hmes/product/material-category',
      component: '@/routes/product/MaterialCategory',
      priority: 10,
    },
    // 事务明细报表
    {
      path: '/hmes/transaction-report/transaction-detail-report',
      component: '@/routes/transactionReport/TransactionDetailReport',
      priority: 100,
    },
    // 移动事件明细报表
    {
      path: '/hmes/transaction-report/mobile-event-detail-report',
      component: '@/routes/transactionReport/MobileEventDetailReport',
      priority: 100,
    },
    // 外部门领用
    {
      path: '/hmes/external-departments-materials',
      component: '@/routes/ExternalDepartmentsMaterials',
      title: '外部门领用',
      authorized: true,
    },
    // 制程时间报表
    {
      path: '/hmes/process-time-report',
      component: '@/routes/ProcessTimeReport',
      authorized: true,
    },
    // 报废明细报表
    {
      path: '/hwms/scrap-detail-report',
      priority: 10,
      routes: [
        {
          path: '/hwms/scrap-detail-report/list',
          priority: 10,
          component: '@/routes/ScrapDetailReport',
        },
      ],
    },
    // 设备OEE报表
    {
      path: '/hmes/equipment-oee-report/list',
      component: '@/routes/EquipmentOeeReport',
      authorized: true,
    },
  ],
  hash: true,
  hzeroMicro: {
    // microConfig: {
    //   registerRegex: '\\/.*',
    // },
  },
  // 如果存在发布 lib 包需求,可以解开该配置，对应 babelrc 中的内容
  // 注意若父模块与子模块都配置了module-resolver插件,请保证数组的第三个参数不能为同一个字符串或者都为空
  extraBabelPlugins: [
    [
      'module-resolver',
      {
        root: ['./'],
        alias: {
          '@': './src',
        },
      },
    ],
  ],
});
