import React, { useMemo } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet, Modal } from 'choerodon-ui/pro';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps, TableQueryBarType } from 'choerodon-ui/pro/lib/table/interface';
import { tableDS } from './stores';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@/utils/config';
import { openTab } from 'utils/menuTab';

const modelPrompt = 'order.work.report';

const OrderWorkReport = observer((props) => {
  const { tableDs } = props;

  const tenantId = getCurrentOrganizationId();

  // 查看合格数详情
  const handleCompletedDetail = (record: any) => {
    const detailDS = new DataSet({
      autoQuery: true,
      autoCreate: false,
      selection: false,
      dataKey: 'rows.content',
      totalKey: 'rows.totalElements',
      fields: [
        {
          name: 'materialLotCode',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
        },
        {
          name: 'eoQty',
          type: FieldType.number,
          label: intl.get(`${modelPrompt}.eoQty`).d('合格数'),
        },
        {
          name: 'lastUpdateDate',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.lastUpdateDate`).d('时间'),
        },
        {
          name: 'lastUpdatedByName',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('操作人'),
        },
      ],
      transport: {
        read: () => ({
          url: `${BASIC.RE_MES_REPORT}/v1/${tenantId}/hme-wo-output-report/completed-detail/query/ui`,
          method: 'POST',
          data: record?.toData() || {},
        }),
      },
    });

    Modal.open({
      title: intl.get(`${modelPrompt}.completedDetail.title`).d('合格数详情'),
      drawer: true,
      width: 1200,
      children: (
        <Table
          dataSet={detailDS}
          columns={[
            { name: 'materialLotCode', width: 200 },
            { name: 'eoQty', width: 120 },
            { name: 'lastUpdateDate', width: 180 },
            { name: 'lastUpdatedByName', width: 150 },
          ]}
          queryBar={TableQueryBarType.none}
        />
      ),
      closable: true,
      destroyOnClose: true,
    });
  };

  // 查看报废入库明细
  const handleScrapDocDetail = (record: any) => {
    const detailDS = new DataSet({
      autoQuery: true,
      autoCreate: false,
      selection: false,
      dataKey: 'rows.content',
      totalKey: 'rows.totalElements',
      fields: [
        {
          name: 'identification',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.identification`).d('EO标识'),
        },
        {
          name: 'eoQty',
          type: FieldType.number,
          label: intl.get(`${modelPrompt}.scrapDocQty`).d('报废入库数'),
        },
        {
          name: 'creationDate',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.creationDate`).d('报废入库时间'),
        },
        {
          name: 'createdByName',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.createdByName`).d('操作人'),
        },
      ],
      transport: {
        read: () => ({
          url: `${BASIC.RE_MES_REPORT}/v1/${tenantId}/hme-wo-output-report/scrap-doc-detail/query/ui`,
          method: 'POST',
          data: record?.toData() || {},
        }),
      },
    });

    Modal.open({
      title: intl.get(`${modelPrompt}.scrapDocDetail.title`).d('报废入库明细'),
      drawer: true,
      width: 1200,
      children: (
        <Table
          dataSet={detailDS}
          columns={[
            { name: 'identification', width: 200 },
            { name: 'eoQty', width: 120 },
            { name: 'creationDate', width: 180 },
            { name: 'createdByName', width: 150 },
          ]}
          queryBar={TableQueryBarType.none}
        />
      ),
      closable: true,
      destroyOnClose: true,
    });
  };

  // 查看产出明细
  const handleOutputDetail = (record: any) => {
    const detailDS = new DataSet({
      autoQuery: true,
      autoCreate: false,
      selection: false,
      dataKey: 'rows.content',
      totalKey: 'rows.totalElements',
      fields: [
        {
          name: 'materialLotCode',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
        },
        {
          name: 'completedQty',
          type: FieldType.number,
          label: intl.get(`${modelPrompt}.completedQty`).d('合格产出数'),
        },
        {
          name: 'scrappedQty',
          type: FieldType.number,
          label: intl.get(`${modelPrompt}.scrappedQty`).d('报废数'),
        },
      ],
      transport: {
        read: () => ({
          url: `${BASIC.RE_MES_REPORT}/v1/${tenantId}/hme-wo-output-report/output-detail/query/ui`,
          method: 'POST',
          data: record?.toData() || {},
        }),
      },
    });

    Modal.open({
      title: intl.get(`${modelPrompt}.outputDetail.title`).d('产出明细'),
      drawer: true,
      width: 700,
      children: (
        <Table
          dataSet={detailDS}
          columns={[
            { name: 'materialLotCode', width: 200 },
            { name: 'completedQty', width: 150 },
            { name: 'scrappedQty', width: 120 },
          ]}
          queryBar={TableQueryBarType.none}
        />
      ),
      closable: true,
      destroyOnClose: true,
    });
  };

  // 查看投入明细
  const handleInputDetail = (record: any) => {
    const detailDS = new DataSet({
      autoQuery: true,
      autoCreate: false,
      selection: false,
      dataKey: 'rows.content',
      totalKey: 'rows.totalElements',
      fields: [
        {
          name: 'materialLotCode',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
        },
        {
          name: 'transTypeCode',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.transTypeCode`).d('事务类型'),
        },
        {
          name: 'primaryUomQty',
          type: FieldType.number,
          label: intl.get(`${modelPrompt}.primaryUomQty`).d('投入数'),
        },
        {
          name: 'transTime',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.transTime`).d('事务时间'),
        },
      ],
      transport: {
        read: () => ({
          url: `${BASIC.RE_MES_REPORT}/v1/${tenantId}/hme-wo-output-report/input-detail/query/ui`,
          method: 'POST',
          data: record?.toData() || {},
        }),
      },
    });

    Modal.open({
      title: intl.get(`${modelPrompt}.inputDetail.title`).d('投入明细'),
      drawer: true,
      width: 1200,
      children: (
        <Table
          dataSet={detailDS}
          columns={[
            { name: 'materialLotCode', width: 200 },
            { name: 'transTypeCode', width: 150 },
            { name: 'primaryUomQty', width: 120 },
            { name: 'transTime', width: 180 },
          ]}
          queryBar={TableQueryBarType.none}
        />
      ),
      closable: true,
      destroyOnClose: true,
    });
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'workOrderNum',
        width: 150,
      },
      {
        name: 'materialCode',
        width: 150,
      },
      {
        name: 'materialName',
        width: 200,
      },
      {
        name: 'planStartTime',
        width: 180,
      },
      {
        name: 'planEndTime',
        width: 180,
      },
      {
        name: 'statusDesc',
        width: 120,
      },
      {
        name: 'lot',
        width: 150,
      },
      {
        name: 'qty',
        width: 120,
      },
      {
        name: 'completedQty',
        width: 120,
        renderer: ({ record }) => {
          return (
            <a onClick={() => handleCompletedDetail(record)}>{record?.get('completedQty') || 0}</a>
          );
        },
      },
      {
        name: 'scrappedQty',
        width: 120,
        renderer: ({ record }) => {
          const workOrderNum = record?.get('workOrderNum');
          const productionLineId = record?.get('productionLineId');
          const startTime = record?.get('planStartTime');
          const endTime = record?.get('planEndTime');
          return (
            <a
              onClick={() => {
                if (workOrderNum) {
                  props.history.push({
                    pathname: '/hwms/scrap-detail-report/list',
                    state: {
                      workOrderNum,
                      productionLineId,
                      startTime,
                      endTime,
                      materialCategory: 'B',
                    },
                  })
                  // openTab({
                  //   key: `/hwms/scrap-detail-report/list`,
                  //   path: `/hwms/scrap-detail-report/list`,
                  //   query: {
                  //     workOrderNum,
                  //     productionLineId,
                  //     startTime,
                  //     endTime,
                  //     materialCategory: 'B',
                  //   },
                  //   title: '报废明细报表',
                  //   closable: true,
                  // });
                }
              }}
            >
              {record?.get('scrappedQty') || 0}
            </a>
          );
        },
      },
      {
        name: 'scrapDocQty',
        width: 120,
        renderer: ({ record }) => {
          return (
            <a onClick={() => handleScrapDocDetail(record)}>{record?.get('scrapDocQty') || 0}</a>
          );
        },
      },
      {
        name: 'inputQty',
        width: 120,
        renderer: ({ record }) => {
          return <a onClick={() => handleInputDetail(record)}>明细</a>;
        },
      },
      {
        name: 'eoQty',
        width: 120,
        renderer: ({ record }) => {
          return <a onClick={() => handleOutputDetail(record)}>{record?.get('eoQty') || 0}</a>;
        },
      },
    ];
  }, []);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('订单报工报表')} />

      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          queryFieldsLimit={4}
          dataSet={tableDs}
          columns={columns}
          searchCode="orderWorkReport"
          customizedCode="orderWorkReport"
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(OrderWorkReport),
);
