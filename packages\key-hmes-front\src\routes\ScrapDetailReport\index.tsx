/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-06-24 17:14:37
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-07-31 13:51:04
 * @FilePath: \re-front\packages\key-hmes-front\src\routes\ScrapDetailReport\index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useMemo, useEffect } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { useDataSetEvent } from 'utils/hooks';
import ExcelExport from 'components/ExcelExport';
import { Header, Content } from 'components/Page';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType, ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { API_HOST, BASIC } from '@/utils/config';
import { tableDS, ngDetailTableDS } from './stores';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.mes.event.ScrapDetailReport';

const ScrapDetailReport = (props) => {
  const scrapDetailDs = useMemo(() => new DataSet(tableDS()), []);
  const ngDetailDs = useMemo(() => new DataSet(ngDetailTableDS()), []);

  useEffect(() => {
    const { workOrderNum, startTime, endTime, materialCategory, productionLineId } =
      props?.location?.state || {};

    const record = scrapDetailDs?.queryDataSet?.current;

    const hasParams = workOrderNum || productionLineId || startTime || endTime || materialCategory;

    if (hasParams && record) {
      // 设置物料类别
      if (materialCategory) {
        record.set('materialCategory', materialCategory);
      }

      // 设置工单 - 需要构造正确的LOV对象结构
      if (workOrderNum) {
        record.set('workOrderObj', [{ workOrderNum, workOrderId: workOrderNum }]);
      }

      // 设置产线 - 需要构造正确的LOV对象结构
      if (productionLineId) {
        record.set('prodLineObj', [{ prodLineId: Number(productionLineId) }]);
      }

      // 设置时间范围
      if (startTime) {
        record.set('startTime', startTime);
      }
      if (endTime) {
        record.set('endTime', endTime);
      }

      // 确保所有参数设置完成后再查询
      setTimeout(() => {
        scrapDetailDs.query();
      }, 100);
    }
  }, [props?.location?.state]);

  const handleScrapRowClick = (record) => {
    if (!record) {
      ngDetailDs.loadData([]); // 没有选中行时清空明细
      return;
    }
    const rowList = record.get('rowList') || [];
    ngDetailDs.loadData([...rowList]);
  };

  /**
   * @description: 主表数据加载/刷新后，自动查询首行对应的行表数据
   */
  useDataSetEvent(scrapDetailDs, 'indexChange', ({ dataSet }) => {
    handleScrapRowClick(dataSet.current);
  });

  const scrapDetailColumns: ColumnProps[] = useMemo(
    () => [
      { name: 'prodLine', width: 120 },
      { name: 'workOrderNum', width: 120 },
      { name: 'materialCode', width: 120 },
      { name: 'materialName', width: 150 },
      { name: 'identification', width: 120 },
      { name: 'eoQty', width: 120 },
      { name: 'completedQty', width: 120 },
      { name: 'scrappedQty', width: 120 },
      { name: 'materialLotCode', width: 120 },
      { name: 'primaryUomQty', width: 120 },
      { name: 'manualNgQty', width: 180 },
    ],
    [],
  );

  const ngDetailColumns: ColumnProps[] = useMemo(
    () => [
      { name: 'defectQty', align: ColumnAlign.left, width: 120 },
      { name: 'ncCode', width: 120 },
      { name: 'ncCodeDesc', width: 150 },
      { name: 'ncRecordStatus', width: 120 },
      { name: 'workcellCode', width: 150 },
      { name: 'operationName', width: 120 },
      { name: 'ncStartUserRealName', width: 120 },
      { name: 'responsibleApartment', width: 120 },
      { name: 'ncStartTime', width: 160 },
      { name: 'remark', width: 120 },
    ],
    [],
  );

  const getExportQueryParams = () => {
    if (!scrapDetailDs.queryDataSet || !scrapDetailDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = scrapDetailDs.queryDataSet.current.toData();
    const { materialCategoryObj, prodLineObj, ...otherQueryParams } = queryParmas || [];
    return otherQueryParams;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('报废明细报表')}>
        <ExcelExport
          method="POST"
          exportAsync
          allBody
          requestUrl={`${API_HOST}${BASIC.RE_MES_REPORT}/v1/${tenantId}/hme_scrap_details_report/export/ui`}
          queryParams={getExportQueryParams}
          fakePost
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          defaultConfig={{ fillerType: 'multi-sheet' }}
        />
      </Header>
      <Content>
        <Table
          queryFieldsLimit={6}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{ fuzzyQuery: false }}
          dataSet={scrapDetailDs}
          columns={scrapDetailColumns}
          searchCode="ScrapDetailReport"
          customizedCode="ScrapDetailReport"
          // onRow={({ record }) => ({
          //   onClick: () => handleScrapRowClick(record),
          // })}
        />
        <Table
          style={{ marginTop: 24 }}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{ fuzzyQuery: false }}
          dataSet={ngDetailDs}
          columns={ngDetailColumns}
          searchCode="NgDetailReport"
          customizedCode="NgDetailReport"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.mes.event.ScrapDetailReport', 'tarzan.common'],
})(ScrapDetailReport);
