/**
 * @Description: 消息处理查询DS
 * @Author: <<EMAIL>>
 * @Date: 2022-01-29 20:32:06
 * @LastEditTime: 2023-08-28 11:17:12
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.message.message.messageProcessing';

const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  primaryKey: 'uniqueCode',
  autoQuery: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'topic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topic`).d('主题'),
    },
    {
      name: 'dealStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dealResult`).d('执行结果'),
      lookupCode: 'MT.MESSAGE.DEAL_STATUS',
    },
    {
      name: 'dateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateFrom`).d('消息处理时间从'),
      max: 'dateTo',
      required: true,
    },
    {
      name: 'dateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateTo`).d('消息处理时间至'),
      min: 'dateFrom',
      required: true,
    },
  ],
  record: {
    dynamicProps: {
      selectable: record => {
        if (record.dataSet.getState('messageType') === 'send') {
          // 查看生产者类型
          const _data = record.toData();

          if (_data.dealStatus === 'RE_SEND_SUCCESS') {
            return false;
          }

          for (let i = 0; i < Object.keys(_data).length; i++) {
            const item = Object.keys(_data)[i];
            if (
              item !== 'dealStatus' &&
              item.includes('dealStatus') &&
              ['SEND_SUCCESS', 'RE_SEND_SUCCESS'].includes(_data.dealStatus) &&
              ['CONSUME_SUCCESS', 'RE_CONSUME_SUCCESS', 'RE_SEND_SUCCESS'].includes(_data[item])
            ) {
              return false;
            }
          }
        } else {
          // 查看消费者类型
          return !['CONSUME_SUCCESS', 'RE_CONSUME_SUCCESS', 'RE_SEND_SUCCESS', 'SEND_SUCCESS'].includes(record.get('dealStatus'));
          // if (record.get('topic').includes('EVENT_HIS_CREATE')) {
          //   return false;
          // }
        }
      },
    },
  },
  fields: [
    {
      name: 'uniqueCode',
      ignore: FieldIgnore.always,
    },
    {
      name: 'topic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topic`).d('主题'),
    },
    {
      name: 'content',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.content`).d('内容'),
    },
    {
      name: 'dealStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dealResult`).d('执行结果'),
      lookupCode: 'MT.MESSAGE.DEAL_STATUS',
    },
    {
      name: 'dealMessage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dealMessage`).d('报错内容'),
    },
    {
      name: 'dealTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dealTime`).d('消息处理时间'),
    },
    {
      name: 'dealTimeCost',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dealTimeCost`).d('处理消息时长(ms)'),
    },
    {
      name: 'reDealTimes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reDealTimes`).d('重复消费次数'),
    },
    {
      name: 'originalMessageId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.originalMessageId`).d('关联来源消息ID'),
    },
  ],
  transport: {
    read: ({ dataSet, data }) => {
      const { services, ...others } = data;
      const _url =
        dataSet?.getState('messageType') === 'send'
          ? `/${services}/v1/${tenantId}/mt-message-send-records/list/ui`
          : `/${services}/v1/${tenantId}/mt-message-receive-records/list/ui`;
      return {
        url: _url,
        method: 'GET',
        data: others,
        transformResponse: res => {
          const originData = JSON.parse(res);

          if (originData?.rows.content?.length) {
            originData.rows.content.forEach((item, index) => {
              // 消息id-时间戳-索引
              item.uniqueCode = `${item.globalMessageId}-${new Date().getTime()}-${index}`;
            });
          }

          return originData;
        },
      };
    },
  },
});

const servicesDS: () => DataSetProps = () => ({
  autoCreate: true,
  fields: [
    {
      name: 'messageType',
      type: FieldType.string,
      required: true,
      defaultValue: 'send',
    },
    {
      name: 'sendServicesCode',
      type: FieldType.string,
      lookupCode: 'MT.MESSAGE_SERVICE',
      required: true,
    },
    {
      name: 'receiveServicesCode',
      type: FieldType.string,
      lookupCode: 'MT.MESSAGE_SERVICE',
      multiple: true,
    },
  ],
});

export { tableDS, servicesDS };
