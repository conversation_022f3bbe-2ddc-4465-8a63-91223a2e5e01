import { extendParentConfig } from '@hzerojs/plugin-micro';

export default extendParentConfig({
  webpack5: {},
  routes: [
    // 设备故障率报表
    {
      path: '/hmes/asset-failure-report',
      component: '@/routes/AssetFailureReport',
    },
    // 原料仓已收待验看板
    {
      path: '/hmes/rework-data',
      component: '@/routes/ReworkData',
      authorized: true,
    },
    // 工艺与工作单元维护
    {
      path: '/hmes/process/unit-work',
      priority: 1000,
      component: '@/routes/process/UnitWork',
    },
    {
      path: '/hmes/organization-modeling/work-cell',
      priority: 1000,
      routes: [
        {
          priority: 1000,
          path: '/hmes/organization-modeling/work-cell/list',
          component: '@/routes/org/WorkCell/WorkCellList',
        },
        {
          priority: 1000,
          path: '/hmes/organization-modeling/work-cell/detail/:workcellId',
          component: '@/routes/org/WorkCell/WorkCellDetail',
        },
      ],
    },
    // 设备与工作单元维护
    {
      path: '/hmes/equipment/equipment-workcell',
      priority: 1000,
      routes: [
        {
          priority: 1000,
          path: '/hmes/equipment/equipment-workcell/list',
          component: '@/routes/equipment/WorkCell',
        },
      ],
    },
    // 站点维护
    {
      path: '/hmes/organization-modeling/site',
      priority: 1000,
      routes: [
        {
          priority: 1000,
          path: '/hmes/organization-modeling/site/list',
          component: '@/routes/org/Site/SiteList',
        },
        {
          priority: 1000,
          path: '/hmes/organization-modeling/site/detail/:siteId',
          component: '@/routes/org/Site/SiteDetail',
        },
      ],
    },
    // 组织关系维护
    {
      path: '/hmes/organization-modeling/relation-maintenance',
      component: '@/routes/org/RelationMaintain',
      model: '@/models/relationMaintain',
      priority: 10,
    },
    {
      path: '/public/hmes/cost-center-approve/:code',
      component: '@/routes/OutFactoryApplyOa',
      authorized: true,
    },
    // 国标码与蓝胶码关系
    {
      path: '/hmes/blue-glue-code-national-standard-code',
      component: '@/routes/BlueGlueCodeNationalStandardCode',
    },
    // 设备查询
    {
      path: '/hmes/device-query',
      component: '@/routes/DeviceQuery',
    },
    // 进出站信息补录
    {
      path: '/hmes/additional-information-recording',
      routes: [
        {
          path: '/hmes/additional-information-recording/list',
          component: '../routes/AdditionalInformationRecording',
        },
      ],
    },
    // 原材料条码追溯报表
    {
      path: '/hmes/raw-material-barcode-traceability-report',
      component: '@/routes/RawMaterialBarcodeTraceabilityReport',
    },
    // // 盘点工作台
    // {
    //   path: '/hmes/inventory/inventory-workbench',
    //   priority: 10,
    //   routes: [
    //     {
    //       path: '/hmes/inventory/inventory-workbench/list',
    //       component: '@/routes/stocktake/StocktakeWorkbench/StocktakeWorkbenchList',
    //       priority: 10,
    //     },
    //     {
    //       path: '/hmes/inventory/inventory-workbench/detail/:id',
    //       component: '@/routes/stocktake/StocktakeWorkbench/StocktakeWorkbenchDetail',
    //       priority: 10,
    //     },
    //   ],
    // },
    // 产品报废记录查询报表
    {
      path: '/hmes/product-scrap-record-query',
      component: '@/routes/ProductScrapRecordQuery',
    },
    // 半成品寿命报表
    {
      path: '/hmes/semi-finished-product-life-report',
      component: '@/routes/SemiFinishedProductLifeReport',
    },
    // 在制品报表
    {
      path: '/hmes/workshop/process-report',
      priority: 10,
      component: '@/routes/workshop/ProcessReport',
    },

    // 盘点工作台-OA
    // {
    //   path: '/public/hmes/inventory/inventory-workbench/:code',
    //   component: '@/routes/stocktake/StocktakeWorkbench/StocktakeWorkbenchListOA',
    //   authorized: true,
    // },
    // 产品加工参数查询
    // {
    //   path: '/hmes/product-processing-parameter-query',
    //   component: '@/routes/ProductProcessingParameterQuery',
    // },
    // 设备加工参数查询报表
    {
      path: '/hmes/product-processing-parameter-report-query',
      component: '@/routes/ProductProcessingParameterReportQuery',
    },
    // 设备与工作单元维护
    {
      path: '/hmes/equipment/equipment-workcell',
      priority: 10,
      routes: [
        {
          path: '/hmes/equipment/equipment-workcell/list',
          component: '@/routes/equipment/WorkCell',
          priority: 10,
        },
      ],
    },
    // 电芯分选等级配置
    {
      path: '/hmes/cell-sorting-level-configuration',
      routes: [
        {
          path: '/hmes/cell-sorting-level-configuration/list',
          component: '../routes/CellSortingLevelConfiguration',
        },
        {
          path: '/hmes/cell-sorting-level-configuration/detail/:id',
          component: '../routes/CellSortingLevelConfiguration/Detail',
        },
      ],
    },
    // 产品批量工序撤销(量产)
    {
      path: '/hmes/product-batch-process-cancellation-mass-production',
      component: '../routes/ProductBatchProcessCancellationMassProduction',
    },
    // 产品批量工序撤销(试制)
    {
      path: '/hmes/product-batch-process-cancellation-trial-produce',
      component: '../routes/ProductBatchProcessCancellationTrialProduce',
    },
    // 生产日报
    {
      path: '/hmes/daily-production-report',
      component: '@/routes/DailyProductionReport',
    },
    // 条码与工位绑定报表
    {
      path: '/hmes/barcode-workstation-binding-report',
      routes: [
        {
          path: '/hmes/barcode-workstation-binding-report/list',
          component: '../routes/BarcodeWorkstationBinding/list/index',
        },
        {
          path: '/hmes/barcode-workstation-binding-report/history',
          component: '../routes/BarcodeWorkstationBinding/list/history',
        },
      ],
    },
    // 制程数据报表
    {
      path: '/hmes/process-data-report',
      authorized: true,
      component: '@/routes/ProcessDataReport',
    },
    // 进出站信息补录平台
    {
      path: '/hmes/information-supplementary-recording',
      component: '../routes/InformationSupplementaryRecording',
    },
    // 在制品导入
    {
      path: '/hmes/wip-import',
      title: '在制品导入',
      component: '../routes/WipImport',
    },
    // 投料罐容量调整
    {
      path: '/hmes/feed-tank-capacity-adjustment',
      routes: [
        {
          path: '/hmes/feed-tank-capacity-adjustment/list',
          component: '../routes/FeedTankCapacityAdjustment',
        },
      ],
    },
    // 领退料工作台-新
    {
      path: '/public/hmes/receive/receive-return-new-oa',
      // authorized: true,
      routes: [
        {
          title: '审批流程URL创建',
          path: `/public/hmes/receive/receive-return-new-oa/list/`,
          component: '@/routes/receive/ReceiveReturn',
          // authorized: true,
        },
        {
          title: '审批流程URL创建',
          path: `/public/hmes/receive/receive-return-new-oa/list/:code`,
          component: '@/routes/receive/ReceiveReturn',
          // authorized: true,
        },
      ],
    },
    {
      path: '/hmes/confirmation-team-working-hours',
      routes: [
        {
          path: '/hmes/confirmation-team-working-hours/list',
          component: '@/routes/ConfirmationTeamWorkingHours',
        },
        {
          path: '/hmes/confirmation-team-working-hours/detail/:id',
          component: '@/routes/ConfirmationTeamWorkingHours/Detail',
        },
      ],
    },
    {
      path: '/hmes/scrap-barcode-generation',
      priority: 1000,
      component: '@/routes/ScrapBarcodeGeneration',
    },
    // 产品结果参数补录
    {
      path: '/hmes/product-processing-parameters-supplement',
      // authorized: true,
      routes: [
        {
          title: '产品结果参数补录',
          path: '/hmes/product-processing-parameters-supplement/list',
          component: '../routes/ProductProcessingParametersSupplement',
        },
      ],
    },
    // 变化点管理
    {
      path: '/hmes/change-point-management',
      component: '@/routes/ChangePointManagement',
    },
    // 产品返修记录查询
    {
      path: '/hmes/query-product-rework-records',
      component: '@/routes/QueryProductReworkRecords',
    },
    // 产品质检记录
    {
      path: '/hmes/product-quality-inspection-record',
      component: '@/routes/ProductQualityInspectionRecord',
    },
    {
      path: '/hmes/tag-standard-rules/list',
      component: '@/routes/ProcessDataReport',
    },
    {
      path: '/hmes/product-quality-inspection-record',
      routes: [
        {
          path: '/hmes/product-quality-inspection-record/list',
          component: '@/routes/ProductQualityInspectionRecord',
        },
        {
          path: '/hmes/product-quality-inspection-record/detail/:id',
          component: '@/routes/ProductQualityInspectionRecord/disposeDetail',
        },
      ],
    },
    // 报检请求管理平台
    {
      path: '/hmes/inspection/inspection-management',
      priority: 10,
      routes: [
        {
          path: '/hmes/inspection/inspection-management/list',
          component: '@/routes/inspection/InspectionManagement',
          priority: 10,
        },
      ],
    },
    // 产品降级记录查询
    {
      path: '/hmes/query-product-degradation-records',
      component: '@/routes/QueryProductDegradationRecords',
    },
    // 不良记录查询
    {
      path: '/hmes/nc-query-record',
      component: '@/routes/NcRecordQuery',
    },
    // 报工事物报表平台
    // {
    //   priority: 10000,
    //   path: '/hmes/work-transaction-report/platform',
    //   component: '@/routes/transactionReport/WorkTransactionReportPlatform',
    // },
    // 设备安灯看板
    {
      path: '/hmes/andeng-monitoring-signboard',
      component: '@/routes/AndengMonitoringSignboard',
    },
    // 报工事物明细报表
    // {
    //   path: '/hmes/work-transaction-report/transaction-detail-report',
    //   component: '@/routes/transactionReport/WorkTransactionDetailReport',
    // },
    // 报工移动事件明细报表
    // {
    //   path: '/hmes/work-transaction-report/mobile-event-detail-report',
    //   component: '@/routes/transactionReport/WorkMobileEventDetailReport',
    // },
    // 标记维护
    {
      path: '/hmes/mark-maintenance',
      routes: [
        {
          path: '/hmes/mark-maintenance/list',
          component: '../routes/MarkMaintenance/MarkMaintenanceList',
        },
        {
          path: '/hmes/mark-maintenance/detail/:id',
          component: '../routes/MarkMaintenance/MarkMaintenanceDetail',
        },
      ],
    },
    // 标记维护-OA
    {
      path: '/public/hmes/mark-maintenance/:code',
      component: '../routes/MarkMaintenance/MarkMaintenanceOA',
      authorized: true,
    },
    // 一键追溯报表
    {
      path: '/hmes/one-trace-report',
      component: '@/routes/OneTraceReport',
    },
    // 条码标记绑定
    {
      path: '/hmes/barcode-marking-binding',
      component: '@/routes/BarcodeMarkingBinding',
    },
    // 条码标记绑定-历史查询
    {
      title: '历史查询',
      path: '/hmes/barcode-marking-binding/history-query',
      component: '@/routes/BarcodeMarkingBinding/HistoryQuery',
    },
    {
      authorized: true,
      path: '/pub/hmes/barcodeMarkingPub/:id',
      component: '@/routes/BarcodeMarkingPub',
    },
    // 设备状态监控报表
    {
      path: '/hmes/equipment-condition-monitoring-report',
      component: '@/routes/EquipmentConditionMonitoringReport',
    },
    // 班组设备关联关系
    {
      path: '/hmes/team-equipment-association-relationship',
      component: '@/routes/TeamEquipmentAssociationRelationship',
    },
    // 事物报表平台
    {
      path: '/hmes/transaction-report/platform',
      component: '@/routes/transactionReport/TransactionReportPlatform',
      priority: 10,
    },
    // 产品加工履历查询报表
    {
      path: '/hmes/product-processing-history-query-report',
      component: '@/routes/ProductProcessingHistoryQueryReport',
    },
    // MARKING报表
    {
      path: '/hmes/marking',
      component: '@/pages/Marking/list/listPage',
    },
    // 批量追溯条码报表
    {
      path: '/hmes/batch-barcode-traceability-report',
      component: '@/routes/BatchBarcodeTraceabilityReport',
    },
    // 条码标记追溯绑定报表
    {
      path: '/hmes/barcode-marking-traceability-report',
      component: '@/routes/BarcodeMarkingTraceabilityReport',
    },
    // 异常品处置
    {
      path: '/hmes/disposal-abnormal-products',
      component: '@/routes/DisposalAbnormalProducts',
    },
    // 装配记录查询
    {
      path: '/hmes/assembly-record-query',
      component: '@/routes/AssemblyRecordQuery',
    },
    // 托唛箱唛电芯码关系查询
    {
      path: '/hmes/special-identification',
      component: '@/routes/SpecialIdentification',
    },
    // 物料库位关系维护
    // {
    //   path: '/hmes/strategy/locator-relation',
    //   routes: [
    //     {
    //       path: '/hmes/strategy/locator-relation/list',
    //       component: '@/routes/strategy/LocatorRelation/LocatorRelationList',
    //     },
    //     {
    //       path: '/hmes/strategy/locator-relation/batch-import',
    //       component: '@/routes/strategy/LocatorRelation/BatchImport',
    //     },
    //   ],
    // },
    // 库存日记账查询
    // {
    //   path: '/hmes/inventory/journal/query',
    //   priority: 10,
    //   component: '@/routes/inventory/JournalQuery/JournalQueryList',
    // },
    // 条码信息查询报表
    {
      path: '/hmes/barcode-info-query-report',
      priority: 1000,
      routes: [
        {
          priority: 1000,
          path: '/hmes/barcode-info-query-report/list',
          component: '@/routes/BarcodeInfoQueryReport/List',
        },
        {
          priority: 1000,
          path: '/hmes/barcode-info-query-report/advanced-query/list',
          component: '@/routes/BarcodeInfoQueryReport/List/AdvancedQueryList',
        },
      ],
    },
    // 特殊参数查询报表
    {
      path: '/hmes/special-parameter-query',
      component: '@/routes/SpecialParameterQuery',
    },
    // 特殊参数同步
    {
      path: '/hmes/special-parameter-sync',
      component: '@/routes/SpecialParameterSync/List',
    },
    // 库存查询
    // {
    //   path: '/hmes/inventory/query/:timer?',
    //   component: '@/routes/Query/QueryList',
    //   priority: 10,
    // },
    // 复采次数配置
    {
      path: '/hmes/wkc-repro-times',
      routes: [
        {
          path: '/hmes/wkc-repro-times/list',
          component: '@/routes/WkcReproTimes/WkcReproTimesList',
        },
      ],
    },
    // 发货报告打印平台
    {
      path: '/hmes/delivery-report-print',
      routes: [
        {
          path: '/hmes/delivery-report-print/list',
          component: '@/routes/DeliveryReportPrint/DeliveryReportPrintList',
        },
      ],
    },
    // 生产版本维护
    {
      path: '/hmes/product/production-version',
      priority: 10,
      component: '@/routes/product/ProductionVersion/index',
    },
    {
      title: '安灯状态监控报表',
      path: '/hmes/andengMonitor',
      component: '@/pages/AndengMonitor/list/listPage',
    },
    {
      title: '料盒物料查询',
      path: '/hmes/cassetteMaterial',
      component: '@/pages/CassetteMaterial/list/listPage',
    },
    {
      title: '电芯装箱',
      path: '/hmes/cell-picking',
      authorized: true,
      component: '@/routes/CellPacking',
    },
    {
      path: '/hmes/production-daily-plan',
      component: '@/routes/ProductionDailyPlan',
    },
    // 迁移产品
    // {
    //   title: '入库单查询',
    //   priority: 1000,
    //   path: '/hmes/inbound/inbound-order-query',
    //   routes: [
    //     {
    //       path: '/hmes/inbound/inbound-order-query/list',
    //       priority: 1000,
    //       component: '@/routes/InboundOrderQuery/InboundOrderQueryList',
    //     },
    //   ],
    // },
    {
      title: '员工登录设备维护',
      path: '/hmes/equipmentMaintenance',
      component: '@/pages/EquipmentMaintenance/list/listPage',
    },
    {
      title: 'SPS接口调用记录',
      path: '/hmes/spsInterfaceRecord',
      component: '@/pages/SpsInterfaceRecord/list/listPage',
    },
    {
      title: '堆叠顺序维护',
      path: '/hmes/stackOrder',
      component: '@/pages/StackOrder/list/listPage',
    },
    {
      title: '班组区域维护-广播系统',
      path: '/hmes/teamMaintenanceRadio',
      component: '@/pages/TeamMaintenanceRadio/list/listPage',
    },
    {
      title: '特殊收集项校验(K值)',
      path: '/hmes/specialCollection',
      routes: [
        {
          path: '/hmes/specialCollection/list',
          component: '@/pages/SpecialCollection/list/listPage',
        },
        {
          path: '/hmes/specialCollection/detail/:id',
          component: '@/pages/SpecialCollection/list/detailPage',
        },
      ],
    },
    {
      title: '首件标记',
      path: '/hmes/firstArticleMarking',
      component: '@/pages/FirstArticleMarking/list/listPage',
    },
    // 组织关系维护
    // {
    //   priority: 1000,
    //   path: '/hmes/organization-modeling/relation-maintenance',
    //   component: '@/routes/RelationMaintain',
    //   model: '@/models/relationMaintain',
    // },
    // 生产线维护
    {
      priority: 1000,
      path: '/hmes/organization-modeling/pro-line',
      routes: [
        {
          path: '/hmes/organization-modeling/pro-line/list',
          component: '@/routes/org/ProLine/ProLineList',
        },
        {
          path: '/hmes/organization-modeling/pro-line/detail/:proLineId',
          component: '@/routes/org/ProLine/ProLineDetail',
        },
      ],
    },
    {
      title: '发货报告角色客户维护',
      path: '/hmes/DeliveryReportRole',
      component: '@/pages/DeliveryReportRole/list/listPage',
    },
    {
      title: '加工控制配置维护',
      path: '/hmes/processing-control-maintenance',
      routes: [
        {
          path: '/hmes/processing-control-maintenance/list',
          component: '../routes/ProcessingControlMaintenance',
        },
      ],
    },
    // 数据收集项维护
    {
      path: '/hmes/acquisition/new-data-item-new',
      routes: [
        {
          path: '/hmes/acquisition/new-data-item-new/list',
          component: '@/pages/acquisition/NewDataItem',
        },
        {
          path: '/hmes/acquisition/new-data-item-new/detail/:id',
          component: '@/pages/acquisition/NewDataItem/Detail',
        },
      ],
    },
    // 数据收集组维护
    {
      path: '/hmes/acquisition/data-collection-new',
      routes: [
        {
          path: '/hmes/acquisition/data-collection-new/list',
          component: '@/pages/acquisition/Collection/CollectionList',
        },
        {
          path: '/hmes/acquisition/data-collection-new/detail/:id',
          component: '@/pages/acquisition/Collection/CollectionDetail',
        },
      ],
    },
    {
      title: '设备接口调用记录-MOM',
      path: '/hmes/device-interface-call-mom',
      routes: [
        {
          path: '/hmes/device-interface-call-mom/list',
          component: '../routes/DeviceInterfaceMom',
        },
      ],
    },
    {
      title: '产品工艺保质期维护',
      path: '/hmes/product-process-shelf-maintenance',
      routes: [
        {
          path: '/hmes/product-process-shelf-maintenance/list',
          component: '../routes/ProductProcessShelfMaintenance',
        },
      ],
    },
    {
      title: '制程数据规格维护',
      path: '/hmes/tag-standard-rules',
      routes: [
        {
          path: '/hmes/tag-standard-rules/list',
          component: '../routes/TagStandardRules',
        },
      ],
    },
    {
      title: '装配组维护',
      path: '/hmes/equipment-group-maintenance',
      routes: [
        {
          path: '/hmes/equipment-group-maintenance/list',
          component: '@/routes/EquipmentGroupMaintenance',
        },
        {
          path: '/hmes/equipment-group-maintenance/:id',
          component: '@/routes/EquipmentGroupMaintenance/Create',
        },
      ],
    },
    {
      title: '装配点维护',
      path: '/hmes/equipment-point-maintenance',
      routes: [
        {
          path: '/hmes/equipment-point-maintenance/list',
          priority: 10,
          component: '../routes/EquipmentPointMaintenanceNew/EquipmentPointMaintenanceList',
        },
        {
          path: '/hmes/equipment-point-maintenance/detail/:id',
          priority: 10,
          component: '../routes/EquipmentPointMaintenanceNew/EquipmentPointMaintenanceDetail',
        },
      ],
    },
    {
      title: '工序反馈参数项配置',
      path: '/hmes/process-feedback-configuration',
      routes: [
        {
          path: '/hmes/process-feedback-configuration/list',
          component: '../routes/ProcessFeedbackConfiguration',
        },
      ],
    },
    {
      title: '工单管理平台',
      path: '/hmes/workorder-management-platform',
      priority: 1000,
      routes: [
        {
          priority: 1000,
          path: '/hmes/workorder-management-platform/list',
          component: '../routes/WorkOrderManagementPlatform',
        },
      ],
    },
    {
      title: '生产工艺折算系数维护',
      path: '/hmes/production-process-conversion',
      routes: [
        {
          path: '/hmes/production-process-conversion/list',
          component: '../routes/ProductionProcessConversion',
        },
      ],
    },
    {
      title: '浆料队列调整',
      path: '/hmes/adjustment-slurry-queue',
      routes: [
        {
          path: '/hmes/adjustment-slurry-queue/list',
          component: '../routes/AdjustmentSlurryQueue',
        },
      ],
    },
    {
      title: '执行作业管理',
      priority: 1000,
      path: '/hwms/workshop/execute-operation-management',
      routes: [
        {
          priority: 1000,
          path: '/hwms/workshop/execute-operation-management/list',
          component: '@/routes/Execute/ExecuteList',
        },
        {
          priority: 1000,
          path: '/hwms/workshop/execute-operation-management/detail/:id',
          component: '@/routes/Execute/ExecuteDetail',
        },
      ],
    },
    // 紧急放行平台
    {
      path: '/hmes/emergency-release-platform',
      routes: [
        {
          path: '/hmes/emergency-release-platform/list',
          component: '@/routes/EmergencyReleasePlatform/List',
        },
        {
          path: '/hmes/emergency-release-platform/detail/:id',
          component: '@/routes/EmergencyReleasePlatform/Detail',
        },
      ],
    },
    // 物料维护
    {
      path: '/hmes/product/material-manager',
      priority: 1000,
      routes: [
        {
          path: '/hmes/product/material-manager/list',
          component: '@/routes/product/Material/MaterialList',
          priority: 1000,
        },
        {
          path: '/hmes/product/material-manager/dist/:id',
          component: '@/routes/product/Material/MaterialDetail',
          priority: 1000,
        },
        {
          path: '/hmes/product/material-manager/site-assignment/:id',
          component: '@/routes/product/Material/MaterialSiteAssignment',
          priority: 1000,
        },
      ],
    },
    // 进出站信息补录（试制）
    {
      path: '/hmes/supplementary-entry-and-exit-information',
      // authorized: true,
      routes: [
        {
          title: '进出站信息补录（试制）',
          path: '/hmes/supplementary-entry-and-exit-information/list',
          component: '../routes/SupplementaryEntryAndExitInformation',
        },
      ],
    },
    // 工单工序在制
    {
      path: '/hmes/work-order-process-in-process',
      component: '@/routes/WorkInProcessReport',
    },
    // 产线分片代码维护
    {
      path: '/hmes/production-line-fragment-code-maintenance',
      component: '../routes/ProductionLineFragmentCodeMaintenance',
    },
    // 在制品入库申请（试制）
    {
      path: '/hmes/application-storage-products-process-trial-prod',
      component: '@/routes/ApplicationStorageProductsProcessTrialProd',
    },
    // 托杯绑定解绑
    {
      path: '/hmes/cup-binding-unbind',
      component: '@/routes/CupBindingUnbind',
    },
    // 蓝胶码钢壳码套膜码关系
    {
      path: '/hmes/blue-rubber-steel-shell-envelope-code-relationship',
      component: '@/routes/BlueRubberSteelShellEnvelopeCodeRelationship',
    },
    // 烘烤加工参数文件上传
    {
      path: '/hmes/bake-processing-parameter-file-upload',
      component: '@/routes/BakeProcessingParameterFileUpload',
    },
    // 物料批工位绑定关系历史
    {
      path: '/hmes/material-lot-binding-relationship-history',
      component: '@/routes/MaterialLotBindingRelationshipHistory',
    },
    // 不良记录管理平台-主界面
    {
      path: '/hmes/bad-record-management-platform',
      component: '@/routes/BadRecordManagementPlatform',
    },
    // 不良记录管理平台-生成不良评审记录单
    {
      title: '生成不良评审记录单',
      path: '/hmes/bad-record-management-platform/generate-bad-review-record-sheet',
      component: '@/routes/BadRecordManagementPlatform/components/GenerateBadReviewRecordSheet',
    },
    // 不良记录管理平台-创建不良
    {
      title: '创建不良',
      path: '/hmes/bad-record-management-platform/create-bad',
      component: '@/routes/BadRecordManagementPlatform/components/CreateBad',
    },
    // 不良处置记录查询
    {
      path: '/hmes/bad-disposal-record-query',
      component: '@/routes/BadDisposalRecordQuery',
    },
    // 报废入库单创建（量产-扫码）
    {
      path: '/hmes/create-scrap-storage-receipt-mass-prod',
      component: '@/routes/CreateScrapStorageReceiptMassProd',
    },
    // 称重报废平台
    {
      path: '/hmes/weighing-scrap-platform',
      component: '@/routes/WeighingScrapPlatform',
    },
    // 进出站平台-量产
    {
      path: '/hmes/entry-and-exit-platform-mass-production',
      component: '@/routes/EntryAndExitPlatformMassProduction',
    },
    // 容器批量绑定/解绑
    {
      path: '/hmes/container-batch-binding-unbind',
      component: '@/routes/ContainerBatchBindingUnbind',
    },
    // 生产日报表
    {
      path: '/hmes/production-daily-report-pro',
      priority: 1000,
      routes: [
        {
          path: '/hmes/production-daily-report-pro/list',
          component: '@/routes/ProductionDailyReportPro',
          priority: 1000,
        },
      ],
    },
    // 本地缓存清理
    {
      path: '/hmes/local/cache/cleanup',
      component: '@/routes/HelpOneselfTo/LocalCacheCleanup',
      authorized: true,
    },
    // 物料批静置时长查询
    {
      path: '/hmes/material-lot-standing-time',
      component: '@/routes/MaterialLotStandingTime',
    },
    // 发货报告打印项目维护
    {
      path: '/hmes/shipment-report-print-maintenance',
      routes: [
        {
          path: '/hmes/shipment-report-print-maintenance/list',
          component: '../routes/ShipmentReportPrintMaintenance',
        },
      ],
    },
    // 容器批量绑定/解绑
    {
      path: '/hmes/container-batch-binding-unbind',
      component: '@/routes/ContainerBatchBindingUnbind',
    },
    // 原材料信息补录
    {
      path: '/hmes/supplementary-material-info',
      component: '@/routes/SupplementaryMaterialInfo',
    },
    // 物料工艺路线-c7n
    {
      path: '/hmes/new/process/routes-c7n',
      title: '物料工艺路线-c7n',
      priority: 1000,
      routes: [
        {
          path: '/hmes/new/process/routes-c7n/list',
          component: '@/routes/process/MaterialProcessRouteC7n/index',
          priority: 1000,
        },
        {
          path: '/hmes/new/process/routes-c7n/dist/:id',
          component: '@/routes/process/MaterialProcessRouteC7n/detail',
          priority: 1000,
        },
      ],
    },
    // 制造工艺路线-c7n
    {
      path: '/hmes/new/manufacture-process/routes-c7n',
      priority: 1000,
      routes: [
        {
          path: '/hmes/new/manufacture-process/routes-c7n/list',
          component: '@/routes/process/ProcessRouteC7n/index',
          priority: 1000,
        },
        {
          path: '/hmes/new/manufacture-process/routes-c7n/dist/:id',
          component: '@/routes/process/ProcessRouteC7n/detail',
          priority: 1000,
        },
      ],
    },
    // 合格率产能报表
    {
      path: '/hmes/ng-out-point',
      component: '@/routes/NgOutPoint',
      authorized: true,
    },
    // 原料仓已收待验看板
    {
      path: '/hmes/has-received',
      component: '@/routes/HasReceived',
    },
    // 成品仓管理
    {
      path: '/hmes/product-warehouse-manage',
      component: '@/routes/ProductWarehouseManage',
    },
    // 合格率产能报表
    {
      path: '/hmes/capacity-report',
      component: '@/routes/CapacityReport',
    },
    // 不良柏拉图报表
    {
      path: '/hmes/bad-plato-report',
      component: '@/routes/BadPlatoReport',
    },
    // 原材料仓管理看板
    {
      path: '/hmes/material-warehouse-manage',
      component: '@/routes/MaterialWarehouseManage',
    },
    // 转单记录查询
    {
      path: '/hmes/material-lot-transfer',
      component: '@/routes/MaterialLotTransfer',
    },
    // 物料批库存查询
    {
      path: '/hmes/material-lot-query',
      component: '@/routes/MaterialLotQuery',
    },
    // 物料批拆分合并关系
    {
      path: '/hmes/material-lot-sm-rel',
      component: '@/routes/MaterialLotSmRel',
    },
  ],
  hash: true,
  hzeroMicro: {
    // microConfig: {
    //   registerRegex: '\\/.*',
    // },
  },
  // 如果存在发布 lib 包需求,可以解开该配置，对应 babelrc 中的内容
  // 注意若父模块与子模块都配置了module-resolver插件,请保证数组的第三个参数不能为同一个字符串或者都为空
  extraBabelPlugins: [
    [
      'module-resolver',
      {
        root: ['./'],
        alias: {
          '@': './src',
          '@components': 'hcm-components-front/lib/components',
          '@services': 'hcm-components-front/lib/services',
          '@utils': 'hcm-components-front/lib/utils',
          '@assets': 'hcm-components-front/lib/assets',
        },
      },
    ],
  ],
});
