/*
 * @Author: jyt <EMAIL>
 * @Date: 2024-11-22 15:01:04
 * @LastEditors: jyt <EMAIL>
 * @LastEditTime: 2024-12-09 15:49:19
 */
import React, { useState } from 'react';
import {
  DataSet,
  Table,
  PerformanceTable,
  Form,
  TextField,
  Button,
  Row,
  Col,
  Modal,
  Lov,
  Output,
  TextArea,
  Icon,
  Select,
} from 'choerodon-ui/pro';
import { Collapse, notification } from 'choerodon-ui';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { flow, keys } from 'lodash';
import withProps from 'utils/withProps';
import { PageHeaderWrapper } from 'components/Page';
import { TableAutoHeightType, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { useDataSetIsSelected, useDataSetEvent } from 'utils/hooks';
import { FieldIgnore, FieldType } from 'choerodon-ui/dataset/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { closeTab } from 'utils/menuTab';

import { infoDS, infoTwoDS, formInfoDS } from '../stores/createBadDS';
import { scanApi, wkcEquipRelApi, ncSubmitApi } from '../service';
import { lovModal } from '../utils';

const intlPrefix = 're.mes.badRecordManagementPlatform';
const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const CreateBad = ({
  tablePgaeDs,
  fields,
  lineDs,
  lineFields,
  formDs,
  match: { path },
  history,
}) => {
  const isSelected = useDataSetIsSelected(tablePgaeDs);
  const isSelectedLine = useDataSetIsSelected(lineDs);

  const [headNum, setHeadNum] = useState(0);
  const [lineNum, setLineNum] = useState(0);

  const columns = [...fields];

  useDataSetEvent(tablePgaeDs, 'select', handleSelect);
  useDataSetEvent(tablePgaeDs, 'unSelect', handleSelect);
  useDataSetEvent(tablePgaeDs, 'batchSelect', handleSelect);
  useDataSetEvent(tablePgaeDs, 'batchUnSelect', handleSelect);

  // Panel头标题和按钮
  function renderPanelHeader(label, rightBtn) {
    return (
      <Row>
        <Col span={4}>{label}</Col>
        <Col
          span={20}
          style={{ display: 'flex', justifyContent: 'flex-end' }}
          onClick={e => e.stopPropagation()}
        >
          {rightBtn}
        </Col>
      </Row>
    );
  }

  // 批量新增不良设备
  function batchAddBadEquipmentModal() {
    const { selected } = tablePgaeDs;
    const ds = new DataSet({
      data: [{}],
      fields: [
        {
          label: '设备',
          name: 'equipmentLov',
          type: FieldType.object,
          lovCode: 'MT.MODEL.EQUIPMENT',
          ignore: FieldIgnore.always,
          required: true,
        },
        { name: 'equipmentCode', bind: 'equipmentLov.equipmentCode' },
        { name: 'equipmentId', bind: 'equipmentLov.equipmentId' },
        { label: '工作单元', name: 'workCellName', bind: 'equipmentLov.workCellName' },
        { name: 'workCellId', bind: 'equipmentLov.workCellId' },
        { name: 'workCellCode', bind: 'equipmentLov.workCellCode' },
        { name: 'workcellCode', bind: 'equipmentLov.workCellCode' },
        { name: 'workcellId', bind: 'equipmentLov.workCellId' },
        { label: '工艺编码', name: 'operationName', bind: 'equipmentLov.operationName' },
        { name: 'operationId', bind: 'equipmentLov.operationId' },
        { name: 'workCellCode', bind: 'equipmentLov.workCellCode' },
        { label: '生产线', name: 'prodLineName', bind: 'equipmentLov.proLineName' },
        { name: 'prodLineId', bind: 'equipmentLov.proLineId' },
        { name: 'prodLineCode', bind: 'equipmentLov.proLineCode' },
      ],
      events: {
        update: ({ record, name, value }) => {
          if (name === 'equipmentLov' && value?.equipmentId) {
            wkcEquipRelApi({
              equipmentId: value.equipmentId,
              equipmentCode: value.equipmentCode,
            }).then(res => {
              if (res && res.success) {
                const { workCellCode, workCellId, workCellName, processWorkcells } =
                  res.rows.wkcList?.[0] || {};
                const {
                  hmeProLineVO: { proLineCode, proLineId, proLineName },
                  operations,
                } = processWorkcells?.[0] || {};
                const { operationId, operationName } = operations?.[0] || {};

                record.set(name, {
                  ...value,
                  workCellCode,
                  workCellId,
                  workCellName,
                  proLineCode,
                  proLineId,
                  proLineName,
                  operationId,
                  operationName,
                });
              }
            });
          }
        },
      },
    });

    Modal.open({
      title: '批量新增不良设备',
      children: (
        <Form dataSet={ds}>
          <Lov name="equipmentLov" />
          <Output name="workCellName" />
          <Output name="operationName" />
          <Output name="prodLineName" />
        </Form>
      ),
      okText: '保存',
      onOk: async () => {
        const vali = await ds?.current?.validate(true);
        const recordData = ds?.current?.toJSONData();

        if (!vali) return false;

        selected.forEach(i => {
          const { ncRecordDetailList = [] } = i.toData();

          keys(recordData).forEach(j => {
            i.set(j, recordData[j]);
            ncRecordDetailList.forEach(r => {
              r[j] = recordData[j];
            });
          });
          i.set('ncRecordDetailList', ncRecordDetailList);
        });
        handleSelect({ dataSet: tablePgaeDs });
      },
    });
  }

  // 批量新增不良明细弹窗
  function batchAddBadDetailModal() {
    const { selected } = tablePgaeDs;
    const ds = new DataSet({
      fields: [
        {
          name: 'ncCodeLov',
          label: '不良代码',
          type: FieldType.object,
          lovCode: 'MT.NC_CODE',
          textField: 'description',
          lovPara: { tenantId },
          multiple: true,
          required: true,
        },
        {
          name: 'responsibleUserLov',
          label: '不良责任人',
          type: FieldType.object,
          lovCode: 'HIAM.USER.ORG',
          ignore: FieldIgnore.always,
          textField: 'realName',
          lovPara: { tenantId },
        },
        { name: 'responsibleUserId', bind: 'responsibleUserLov.id' },
        { name: 'responsibleUserName', bind: 'responsibleUserLov.realName' },
        {
          label: '不良责任部门',
          name: 'responsibleApartment',
          lookupCode: 'RE.NC_UNIT',
          required: true,
        },
        { label: '备注', name: 'remark' },
      ],
    });

    Modal.open({
      title: '批量新增不良明细',
      children: (
        <Form dataSet={ds}>
          <Lov name="ncCodeLov" />
          <Lov name="responsibleUserLov" />
          <Select name="responsibleApartment" />
          <TextArea name="remark" />
        </Form>
      ),
      okText: '保存',
      onOk: async () => {
        const vali = await ds?.current?.validate(true);

        if (!vali) return false;

        const { ncCodeLov, ...other } = ds?.current?.toJSONData();
        const ncCodeList = ncCodeLov.map(i => i.ncCode);
        const checkDuplicateData = selected
          .map(i => i.toData())
          .find(i => {
            const { ncRecordDetailList } = i;

            return ncRecordDetailList?.some(j => ncCodeList.includes(j.ncCode));
          });

        if (checkDuplicateData) {
          const ncCode = checkDuplicateData.ncRecordDetailList.find(i =>
            ncCodeList.includes(i.ncCode),
          );

          notification.error({
            message: `物料批编码: ${checkDuplicateData.identification} 已存在 ${ncCode.description}，请勿重复添加！`,
            description: undefined,
          });

          return false;
        }

        selected.forEach(i => {
          ncCodeLov.forEach(j => {
            const { ncRecordDetailList, ...iOther } = i.toData();
            i.set(
              'ncRecordDetailList',
              (ncRecordDetailList || [])
                .concat([
                  {
                    ...iOther,
                    ...j,
                    ...other,
                    ncCodeDesc: j.description,
                    ncCodeStatus: 'NEW',
                    identification: iOther.keyCode,
                  },
                ])
                .map((r, index) => ({
                  ...r,
                  lineNumber: index + 1,
                })),
            );
          });
        });

        handleSelect({ dataSet: tablePgaeDs });
      },
    });
  }

  // 条码扫描
  async function handleScan() {
    const vali = await formDs.current.validate(true);
    const formData = formDs.current.toData();
    const repeatVali =
      tablePgaeDs.length &&
      tablePgaeDs.some(i =>
        formData.identification.includes(i.get('identification') || i.get('materialLotCode')),
      );

    if (!vali) return;

    if (!repeatVali) {
      tablePgaeDs.status = 'loading';

      const res = await scanApi({
        ...formData,
      });

      if (res && res.success) {
        const data = res.rows;
        data.map(i => ({
          ...i,
          identification: i.ncRecordType === 'RM_NC' ? i.materialLotCode : i.identification,
        }));
        // .forEach(i => {
        //   tablePgaeDs.create(i, 0);
        // });
        tablePgaeDs.loadData(data);
      } else {
        notification.error({
          message: res.message,
          description: undefined,
        });
      }

      tablePgaeDs.status = 'ready';
    } else {
      notification.warning({
        message: '条码重复，请重新扫描！',
        description: undefined,
      });
    }

    formDs.current.set('identification', null);
  }

  // 勾选头显示明细数据
  function handleSelect({ dataSet }) {
    const { selected } = dataSet;
    setHeadNum(selected.length);
    const data = selected.reduce((a, b) => {
      const { ncRecordDetailList } = b.toData();

      return a.concat(ncRecordDetailList || []);
    }, []);
    setLineNum(data.length);
    lineDs.loadData(data);
  }

  // 提交
  async function handleSubmit() {
    const data = tablePgaeDs.toData();
    const obj = data.find(i => !i?.ncRecordDetailList?.length);

    if (!data.length) {
      notification.warning({
        message: '暂无可提交数据！',
        description: undefined,
      });
      return;
    }

    if (obj) {
      notification.warning({
        message: `物料批编码: ${obj.keyCode} 未添加不良明细，请添加！`,
        description: undefined,
      });
      return;
    }

    const res = await ncSubmitApi({
      ncRecordAndDetailList: data.map(i => {
        const { ncRecordDetailList, ...other } = i;

        return {
          ncRecordInfo: other,
          ncRecordDetailList: ncRecordDetailList.map(j => ({
            ...j,
            rootCauseWorkcellId: j.workCellId,
            rootCauseEquipmentId: j.equipmentId,
            rootCauseOperationId: j.operationId,
          })),
        };
      }),
    });

    if (res && res.success) {
      notification.success({
        message: '提交成功',
        description: undefined,
      });
      setTimeout(() => {
        history.push({
          pathname: '/hmes/nc-query-record',
          state: {
            identifications: data.map(i => i.identification).join(','),
          },
        });
        closeTab(path);
      }, 500);
    } else {
      notification.error({
        message: res.message,
        description: undefined,
      });
    }
  }

  // 头删除
  async function handleHeadDelete() {
    const { selected } = tablePgaeDs;

    const res = await tablePgaeDs.delete(selected);

    if (res !== false) {
      const selectedData = selected.map(i => i.get('identification'));
      const selectedArr = lineDs.filter(i => selectedData.includes(i.get('identification')));
      lineDs.delete(selectedArr, false);
    }
  }

  // 行删除
  async function handleLineDelete() {
    const { selected } = lineDs;

    const res = await lineDs.delete(selected);

    if (res !== false) {
      tablePgaeDs.forEach(i => {
        const { ncRecordDetailList, identification } = i.toData();

        if (selected.some(j => j.get('identification') === identification)) {
          const filterArr = selected
            .filter(j => j.get('identification') === identification)
            .map(j => j.get('ncCodeId'));

          i.set(
            'ncRecordDetailList',
            ncRecordDetailList.filter(j => !filterArr.includes(j.ncCodeId)),
          );
        }
      });
    }
  }

  return (
    <div className="hmes-style">
      <PageHeaderWrapper
        title={intl.get(`${intlPrefix}.title.badReviewRecordSheet`).d('创建不良')}
        header={[
          <Button color={ButtonColor.primary} onClick={handleSubmit}>
            提交
          </Button>,
        ]}
      >
        <Collapse
          bordered={false}
          defaultActiveKey={['recordInfoSheet', 'objectInfo', 'recordDetail']}
        >
          <Panel
            header={intl.get(`${intlPrefix}.title.badRecordInfoSheet`).d('不良记录信息单')}
            key="recordInfoSheet"
            showArrow={false}
          >
            <Form dataSet={formDs} columns={3} labelWidth="auto">
              <TextField
                name="identification"
                maxTagCount={2}
                suffix={
                  <Icon
                    type="search"
                    onClick={() =>
                      lovModal({
                        title: formDs.getField('identification').get('label'),
                        name: 'identification',
                        record: formDs?.current,
                        okFun: handleScan,
                      })
                    }
                  />
                }
                onEnterDown={handleScan}
              />
              <Icon type="refresh" onClick={handleScan} />
            </Form>
          </Panel>
          <Panel
            header={renderPanelHeader(
              <div>
                {intl.get(`${intlPrefix}.title.badRecordInfoSheet`).d('不良对象信息')}(
                {intl.get(`${intlPrefix}.title.badRecordInfoSheetNum`).d('不良对象数量')}:{headNum})
              </div>,
              [
                <Button
                  disabled={!isSelected}
                  color={ButtonColor.primary}
                  onClick={batchAddBadEquipmentModal}
                >
                  批量新增不良设备
                </Button>,
                <Button
                  disabled={!isSelected}
                  color={ButtonColor.primary}
                  onClick={batchAddBadDetailModal}
                >
                  批量新增不良明细
                </Button>,
                <Button disabled={!isSelected} color={ButtonColor.red} onClick={handleHeadDelete}>
                  删除
                </Button>,
              ],
            )}
            key="objectInfo"
            showArrow={false}
          >
            <Table
              dataSet={tablePgaeDs}
              columns={columns}
              queryBar={TableQueryBarType.none}
              autoHeight={{ type: TableAutoHeightType.minHeight, diff: 0 }}
              virtual
              virtualCell
            />
          </Panel>
          <Panel
            header={renderPanelHeader(
              <div>
                {intl.get(`${intlPrefix}.title.badPObjectInfo`).d('不良记录明细')}(
                {intl.get(`${intlPrefix}.title.badPObjectInfoNum`).d('不良明细数量')}: {lineNum})
              </div>,
              [
                <Button
                  color={ButtonColor.red}
                  disabled={!isSelectedLine}
                  onClick={handleLineDelete}
                >
                  删除
                </Button>,
              ],
            )}
            key="recordDetail"
            showArrow={false}
          >
            <Table
              dataSet={lineDs}
              columns={lineFields}
              autoHeight={{ type: TableAutoHeightType.minHeight, diff: 0 }}
              virtual
              virtualCell
            />
          </Panel>
        </Collapse>
      </PageHeaderWrapper>
    </div>
  );
};

export default flow(
  formatterCollections({ code: [intlPrefix, 'tarzan.common'] }),
  withProps(
    () => {
      const tablePgaeDs = new DataSet({ ...infoDS(), paging: false });
      const lineDs = new DataSet({ ...infoTwoDS(), paging: false });
      const formDs = new DataSet({ ...formInfoDS() });
      const fields = infoDS().fields;
      const lineFields = infoTwoDS().fields;

      return {
        tablePgaeDs,
        lineDs,
        formDs,
        lineFields,
        fields,
      } as any;
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
)(CreateBad);
