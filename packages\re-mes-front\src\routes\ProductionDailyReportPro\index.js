/*
 * @Author: 40463 <EMAIL>
 * @Date: 2025-06-11 17:43:21
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-07-11 16:04:42
 * @FilePath: \re-front\packages\re-mes-front\src\routes\ProductionDailyReportPro\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useMemo, useEffect, useState } from 'react';
import { DataSet, Table, Modal, DatePicker } from 'choerodon-ui/pro';
import { notification } from 'hzero-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import { Header, Content } from 'components/Page';
import ExcelExport from 'components/ExcelExport';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import moment from 'moment';
import { tableDS, DetailDS, modalDS, investDetailDS } from './stores/tableDS';
import { getProductionDailyDetail } from '../../services/api';

const modelPrompt = 'tarzan.hmes.ProductionProcessConversion';
const organizationId = getCurrentOrganizationId();
const ProductionProcessConversion = () => {
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const detailDs = useMemo(() => new DataSet(DetailDS()), []);
  const modalDs = useMemo(() => new DataSet(modalDS()), []);
  const investDetailDs = useMemo(() => new DataSet(investDetailDS()), []);

  // useEffect(() => {
  //   tableDs.query();
  // }, []);

  const [detailData, setDetailData] = useState(null);
  const openDetail = async record => {
    console.log(record.toData());
    const data = record.toData();

    detailDs.queryParameter = {
      prodLineCode: data.prodLineCode,
      sourceOrder: data.sourceOrder,
      materialCode: data.materialCode,
      startTime: data.startTime,
      endTime: data.endTime,
    };
    console.log('当前查询参数:', detailDs.queryParameter);
    // setTimeout(() => { detailDs.query() }, 1000)
    await detailDs.query();
    Modal.open({
      destroyOnClose: true, // 关闭时是否销毁
      style: {
        width: 560,
      },
      closable: true,
      title: '投料明细',
      footer: null,
      children: <Table dataSet={detailDs} columns={columns1} />,
    });
  };

  const openInvestDetail = async params => {
    investDetailDs.queryParameter = params;
    await investDetailDs.query();
    Modal.open({
      destroyOnClose: true,
      style: { width: 560 },
      closable: true,
      title: '投入明细',
      footer: null,
      children: (
        <>
          <div style={{ display: 'flex', justifyContent: 'end' }}>
            <ExcelExport
              requestUrl={`/re-report-mes/v1/${organizationId}/hme-prod-daily-report/input/detail/export/ui`}
              method="POST"
              queryParams={params}
              fakePost
            />
          </div>
          <Table dataSet={investDetailDs} columns={columns2} />
        </>
      ),
    });
  };
  const openDetailModal = async (record, type) => {
    const data = record.toData();

    if (type === 'actualQty') {
      data.inputNum = data.actualQty;
      data.isQualified = true;
    } else {
      data.inputNum = data.ngQty;
      data.isQualified = false;
    }
    console.log(data);

    setDetailData(data);
    try {
      const params = {
        ...data,
        startTime: data.startTime,
        endTime: data.endTime,
      };
      const res = await getProductionDailyDetail(params);
      if (!res.failed) {
        // 确保数据是数组格式
        const tableData = Array.isArray(res) ? res : [res];

        modalDs.loadData(tableData);
        Modal.open({
          destroyOnClose: true,
          style: { width: 560 },
          closable: true,
          title: '产量明细',
          footer: null,
          children: (
            <>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <ExcelExport
                  requestUrl={`/re-report-mes/v1/${organizationId}/hme-prod-daily-report/materialLot/export/list/ui`}
                  method="POST"
                  queryParams={params}
                  fakePost
                />
              </div>

              <Table dataSet={modalDs} columns={columns2} />
            </>
          ),
        });
      } else {
        notification.error({ message: res.message });
      }
    } catch (error) {
      console.error('打开模态框出错:', error);
      notification.error({ message: '加载数据失败' });
    }
  };
  const onExport = () => {
    const exportData = tableDs.queryDataSet.toData()[0];
    for (const i of Object.keys(exportData)) {
      if (typeof exportData[i] === 'object') {
        delete exportData[i];
      }
    }
    return {
      ...exportData,
      ...tableDs.queryParameter,
      startTime: exportData.startTime,
      endTime: exportData.endTime,
    };
  };

  // 投入明细点击事件
  const handleInvestDetail = record => {
    const queryData =
      tableDs.queryDataSet && tableDs.queryDataSet.current && tableDs.queryDataSet.current.toData
        ? tableDs.queryDataSet.current.toData()
        : {};
    openInvestDetail({
      prodLineCode: record.get('prodLineCode'),
      materialCode: record.get('materialCode'),
      sourceOrder: record.get('sourceOrder'),
      startTime: queryData.startTime,
      endTime: queryData.endTime,
    });
  };

  const columns = [
    { name: 'prodLineCode' },
    { name: 'prodLineName' },
    { name: 'materialCode' },
    { name: 'materialName' },
    { name: 'sourceOrder' },
    { name: 'lot' },
    { name: 'startTime' },
    { name: 'endTime' },
    { name: 'planQty' },
    {
      name: 'actualQty',
      renderer: ({ record, text }) => (
        <div>
          <a onClick={() => openDetailModal(record, 'actualQty')}>{text}</a>
        </div>
      ),
    },
    {
      name: 'ngQty',
      renderer: ({ record, text }) => (
        <div>
          <a onClick={() => openDetailModal(record, 'ngQty')}>{text}</a>
        </div>
      ),
    },
    // { name: 'planAchievementRate' },
    { name: 'primaryUomCode' },
    {
      // name: 'planAchievementRate',
      title: '操作',
      renderer: ({ record }) => (
        <div>
          <a onClick={() => openDetail(record)}>详情</a>
        </div>
      ),
    },
  ];
  const columns1 = [
    { name: 'materialCode' },
    { name: 'materialName' },
    { name: 'primaryUomCode' },
    {
      name: 'primaryUomQty',
      renderer: ({ record, text }) => (
        <a
          style={{ color: '#1890ff', cursor: 'pointer' }}
          onClick={() => handleInvestDetail(record)}
        >
          {text}
        </a>
      ),
    },
  ];
  const columns2 = [{ name: 'materialLotCode' }, { name: 'num' }, { name: 'lot' }];

  return (
    <React.Fragment>
      <Header title={intl.get(`${modelPrompt}.title`).d('生产日报表')}>
        <ExcelExport
          requestUrl={`/re-report-mes/v1/${organizationId}/hme-prod-daily-report/export/list/ui`}
          queryParams={onExport}
        />
      </Header>

      <Content>
        {/* <Spin spinning={loading}> */}

        <Table
          dataSet={tableDs}
          columns={columns}
          // queryBar="filterBar"
          // queryBarProps={{
          //   fuzzyQuery: false,
          // }}
          queryFields={{
            startTime: (
              <DatePicker
                mode="dateTime"
                style={{ width: '100%' }}
                format="YYYY-MM-DD HH:mm:ss"
                defaultTime={moment('08:30:00', 'HH:mm:ss')}
              />
            ),
            endTime: (
              <DatePicker
                mode="dateTime"
                style={{ width: '100%' }}
                format="YYYY-MM-DD HH:mm:ss"
                defaultTime={moment('08:30:00', 'HH:mm:ss')}
              />
            ),
          }}
          queryFieldsLimit={4}
        />
        {/* </Spin> */}
      </Content>
    </React.Fragment>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.ProductionProcessConversion', 'tarzan.common'],
})(ProductionProcessConversion);
