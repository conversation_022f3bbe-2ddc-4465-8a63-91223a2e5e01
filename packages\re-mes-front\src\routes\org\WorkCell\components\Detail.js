/**
 * @Description: 工作单元维护-详情页组件
 * @Author: <<EMAIL>>
 * @Date: 2021-02-18 16:54:28
 * @LastEditTime: 2023-05-18 11:33:47
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo, useEffect, useImperativeHandle, forwardRef } from 'react';
import { DataSet, TextField, Form, Switch, Select, IntlField, Tabs } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import notification from 'utils/notification';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import { isEmpty } from 'lodash';
import { detailDS } from '../stores/WorkCellDS';
import ProduceInfoTab from './ProduceInfoTab';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const modelPrompt = 'tarzan.model.org.workcell';

const Detail = (props, ref) => {
  const {
    canEdit,
    kid,
    columns = 1,
    componentType,
    customizeForm,
  } = props;
  const detailDs = useMemo(() => new DataSet(detailDS()), []);

  useEffect(() => {
    if (kid !== 'create') {
      detailQuery(kid);
    }
  }, [kid]);

  const detailQuery = id => {
    detailDs.setQueryParameter('workcellId', id);
    detailDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.WORKCELL_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.WORKCELL`)
    detailDs.query();
  };

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    submit: async () => {
      detailDs.current.set({ nowDate: new Date().getTime() }); // 强制修改DataSet,否则新建的数据不会校验

      const validate = await detailDs.validate();
      if (validate) {
        let success = false;
        let newKid = '';
        await detailDs.submit().then(res => {
          const { rows = [] } = res || {};
          if (!isEmpty(rows) && rows[0].success) {
            notification.success({});
            success = true;
            newKid = res.rows[0].rows;
            detailQuery(newKid);
          }
        });
        return { success, newKid };
      }
      return { success: false };
    },
    reset: () => {
      detailQuery(kid);
    },
  }));

  const customizeCode = useMemo(() => {
    switch (componentType) {
      case 'WORKCELL':
        return `${BASIC.CUSZ_CODE_BEFORE}.WORKCELL_DETAIL.BASIC`;
      case 'ORG_RELATION':
        return `${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.WORKCELL`;
      default:
        console.error('父组件传入类型错误！')
        break;
    }
  }, [componentType]);

  const childProps = {
    canEdit,
    columns,
  };

  return (
    <>
      <Collapse bordered={false} defaultActiveKey={['workcellInfo']}>
        <Panel
          header={intl.get(`${modelPrompt}.workcellInfo`).d('工作单元信息')}
          key="workcellInfo"
          dataSet={detailDs}
        >
          {customizeForm(
            {
              code: customizeCode,
            },
            <Form
              disabled={!canEdit}
              dataSet={detailDs}
              columns={columns}
              labelLayout="horizontal"
              labelWidth={112}
            >
              <TextField name="workcellCode" />
              <IntlField
                name="workcellName"
                modalProps={{
                  title: intl.get(`${modelPrompt}.workcellName`).d('工作单元短描述'),
                }}
              />
              <IntlField
                name="description"
                modalProps={{
                  title: intl.get(`${modelPrompt}.description`).d('工作单元长描述'),
                }}
              />
              <IntlField
                name="workcellLocation"
                modalProps={{
                  title: intl.get(`${modelPrompt}.workcellLocation`).d('工作单元位置'),
                }}
              />
              <Select name="workcellType" />
              <Switch name="enableFlag" />
            </Form>,
          )}
        </Panel>
      </Collapse>
      <Tabs activeKey="produce">
        <TabPane
          tab={intl.get(`${modelPrompt}.produceInfo`).d('生产属性')}
          key="produce"
          forceRender
        >
          <ProduceInfoTab ds={detailDs} {...childProps} />
        </TabPane>
      </Tabs>
    </>
  );
};

export default withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.WORKCELL_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.WORKCELL`],
})(forwardRef(Detail));
